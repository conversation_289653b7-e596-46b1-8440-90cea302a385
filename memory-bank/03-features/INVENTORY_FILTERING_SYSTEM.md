# Inventory Filtering System

## Overview

The Inventory Filtering System provides comprehensive filtering capabilities for inventory items, allowing users to search and filter their pantry inventory using multiple criteria including location, category, stock status, expiration status, date ranges, and more.

## Features

### 1. Text Search
- **Query Parameter:** `query`
- **Functionality:** Searches across product names, variant names, brands, and notes
- **Implementation:** Case-insensitive LIKE search using PostgreSQL
- **Example:** `?query=milk` finds all items containing "milk" in any searchable field

### 2. Location Filtering
- **Single Location:** `location_id` - Filter by specific location UUID
- **Multiple Locations:** `location_ids[]` - Filter by multiple location UUIDs (comma-separated)
- **Use Case:** Find all items in specific pantry locations (e.g., "Main Fridge", "Kitchen Cabinet")

### 3. Category Filtering
- **Single Category:** `category_id` - Filter by specific category UUID
- **Multiple Categories:** `category_ids[]` - Filter by multiple category UUIDs
- **Include Subcategories:** `include_subcategories=true` - Include items from subcategories
- **Use Case:** Find all dairy products including subcategories like "Milk", "Cheese", "Yogurt"

### 4. Product Filtering
- **Product ID:** `product_id` - Filter by specific product UUID
- **Product Variant:** `product_variant_id` - Filter by specific variant UUID
- **Brand:** `brand` - Filter by product brand (case-insensitive exact match)
- **Use Case:** Find all items from a specific brand or product line

### 5. Stock Status Filtering
- **Parameter:** `stock_status`
- **Values:**
  - `low` - Items with quantity > 0 and < 5 (configurable threshold)
  - `out` - Items with quantity = 0
  - `well_stocked` - Items with quantity >= 5 (configurable threshold)
  - `all` - No stock filtering
- **Use Case:** Identify items that need restocking

### 6. Expiration Status Filtering
- **Parameter:** `expiration_status`
- **Values:**
  - `expired` - Items past expiration date
  - `warning` - Items expiring within 7 days
  - `alert` - Items expiring within 3 days
  - `critical` - Items expiring within 1 day
- **Additional Parameters:**
  - `expiring_in_days` - Custom expiration threshold in days
  - `expired_only=true` - Show only expired items
- **Use Case:** Manage food safety and reduce waste

### 7. Date Range Filtering
- **Purchase Date:** `purchase_date_from`, `purchase_date_to` (YYYY-MM-DD format)
- **Creation Date:** `created_from`, `created_to` (YYYY-MM-DD format)
- **Use Case:** Find items purchased or added within specific time periods

### 8. Quantity Range Filtering
- **Min Quantity:** `min_quantity` - Minimum quantity threshold
- **Max Quantity:** `max_quantity` - Maximum quantity threshold
- **Use Case:** Find items within specific quantity ranges

### 9. Sorting Options
- **Sort By:** `sort_by`
  - `created_at` - Creation date (default)
  - `updated_at` - Last update date
  - `quantity` - Item quantity
  - `expiration_date` - Expiration date
  - `purchase_date` - Purchase date
  - `product_name` - Product name
  - `variant_name` - Variant name
  - `category_name` - Category name
  - `location_name` - Location name
  - `brand` - Product brand
- **Sort Order:** `sort_order`
  - `desc` - Descending (default)
  - `asc` - Ascending

### 10. Pagination
- **Page:** `page` - Page number (default: 1)
- **Limit:** `limit` - Items per page (default: 20, max: 100)

## API Endpoint

### GET /pantries/{pantryId}/inventory/filter

**Description:** Advanced filtering for inventory items with comprehensive search and filter options.

**Authentication:** Required (Bearer token)

**Parameters:** All parameters are optional query parameters

**Example Request:**
```
GET /api/v1/pantries/123e4567-e89b-12d3-a456-426614174000/inventory/filter?location_id=456e7890-e89b-12d3-a456-426614174001&category_id=789e0123-e89b-12d3-a456-426614174002&include_subcategories=true&stock_status=low&sort_by=expiration_date&sort_order=asc&page=1&limit=20
```

**Response Format:**
```json
{
  "success": true,
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "pantry_id": "456e7890-e89b-12d3-a456-426614174001",
      "pantry_name": "Main Kitchen",
      "location_id": "789e0123-e89b-12d3-a456-426614174002",
      "location_name": "Main Fridge",
      "product_variant_id": "012e3456-e89b-12d3-a456-426614174003",
      "product_name": "Organic Milk",
      "product_brand": "Farm Fresh",
      "variant_name": "1L Whole Milk",
      "variant_image_url": "https://example.com/images/milk-1l.jpg",
      "category_name": "Dairy",
      "unit_of_measure_id": "345e6789-e89b-12d3-a456-426614174004",
      "unit_name": "Liter",
      "unit_symbol": "L",
      "quantity": 2.5,
      "expiration_date": "2025-06-20T00:00:00Z",
      "purchase_date": "2025-06-10T00:00:00Z",
      "purchase_price": 4.99,
      "notes": "Low stock - need to reorder",
      "status": "warning",
      "created_at": "2025-06-10T10:30:00Z",
      "updated_at": "2025-06-12T15:45:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "total_pages": 3,
    "has_next": true,
    "has_prev": false
  },
  "message": "Filtered pantry inventory retrieved successfully",
  "timestamp": "2025-06-12T19:30:00Z",
  "request_id": "req_123456789"
}
```

## Implementation Details

### Database Query Optimization
- Uses JOIN operations to fetch all related data in a single query
- Implements proper indexing for filter columns
- Supports complex WHERE clauses with multiple conditions
- Efficient pagination with OFFSET and LIMIT

### Security
- Requires authentication via Bearer token
- Validates user permissions for pantry access
- Sanitizes all input parameters
- Prevents SQL injection through parameterized queries

### Performance Considerations
- Configurable stock thresholds to avoid hardcoded values
- Efficient query building with conditional WHERE clauses
- Proper database indexing on frequently filtered columns
- Pagination to limit result set size

### Error Handling
- Validates all input parameters
- Returns appropriate HTTP status codes
- Provides detailed error messages for debugging
- Logs errors for monitoring and troubleshooting

## Use Cases

### 1. Kitchen Management
- Find all items in the refrigerator that are expiring soon
- Check stock levels for specific categories before cooking
- Locate items by brand or product type

### 2. Shopping List Generation
- Identify low stock items that need replenishing
- Find expired items that need replacement
- Check inventory before grocery shopping

### 3. Inventory Auditing
- Review items purchased within a specific date range
- Analyze inventory by location and category
- Track quantity changes over time

### 4. Food Safety Management
- Monitor expiration dates across all categories
- Identify items that need immediate attention
- Ensure proper rotation of perishable goods

## Testing

The filtering system includes comprehensive integration tests that verify:
- All filter parameters work correctly
- Pagination functions properly
- Sorting options are applied correctly
- Security permissions are enforced
- Error handling works as expected

Test coverage includes scenarios for:
- Single and multiple filter combinations
- Edge cases (empty results, invalid parameters)
- Permission validation
- Database query optimization

## Future Enhancements

### Planned Features
1. **Saved Filters** - Allow users to save frequently used filter combinations
2. **Smart Suggestions** - AI-powered filter suggestions based on usage patterns
3. **Bulk Actions** - Apply actions to filtered results (e.g., bulk update, delete)
4. **Export Options** - Export filtered results to CSV or PDF
5. **Advanced Analytics** - Generate reports based on filtered data

### Performance Improvements
1. **Caching** - Cache frequently used filter results
2. **Search Indexing** - Implement full-text search for better text search performance
3. **Query Optimization** - Further optimize complex filter combinations
4. **Real-time Updates** - WebSocket support for real-time inventory updates

## Integration

The filtering system integrates seamlessly with:
- **Inventory Management** - Core inventory CRUD operations
- **Shopping Lists** - Generate lists based on filtered results
- **Expiration Tracking** - Monitor items based on expiration filters
- **Recipe Management** - Check ingredient availability using filters
- **Notification System** - Alert users about filtered conditions

## Documentation

- **API Documentation** - Available in Swagger/OpenAPI format
- **Postman Collection** - Complete with example requests and responses
- **Integration Guide** - Frontend integration examples for Next.js PWA
- **Test Documentation** - Comprehensive test scenarios and expected results
