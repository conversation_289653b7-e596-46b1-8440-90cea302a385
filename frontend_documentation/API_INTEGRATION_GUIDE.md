# API Integration Guide for Next.js PWA (Pantry Pal)

## Overview

This guide provides the authoritative reference for integrating the Pantry Pal backend API with a Next.js PWA frontend. It covers authentication, endpoint conventions, enhanced response fields, error handling, pagination, idempotency, and all major resource endpoints, reflecting the latest backend implementation and conventions.

---

## 1. API Base URL and Authentication

* **Base URL:**  
`https://api.pantrypal.com/api/v1` (production)  
`http://localhost:8080/api/v1` (development)

* **Authentication:**  
  All protected endpoints require a JWT Bearer token in the `Authorization` header.  
  Example:  
  

```
  Authorization: Bearer {access_token}
  ```

* **Authentication Endpoints:**
  + `POST /auth/register` — Register a new user
  + `POST /auth/login` — Login and obtain tokens
  + `POST /auth/refresh` — Refresh access token using refresh token
  + `POST /auth/logout` — Logout and revoke refresh token
  + `POST /users/change-password` — Change password (requires current password)

* **User Profile:**
  + `GET /users/profile` — Get current user profile
  + `PUT /users/profile` — Update profile

---

## 2. Standard Response Format

All API responses use a consistent structure:

```typescript
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ErrorInfo;
  message?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  request_id?: string;
}
interface ErrorInfo {
  code: string;
  message: string;
  details?: Record<string, any>;
}
interface PaginatedResponse<T = any> extends APIResponse<T> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}
```

**Enhanced Inventory Example:**

```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "pantry_id": "456e7890-e89b-12d3-a456-426614174001",
    "pantry_name": "Main Kitchen",
    "location_id": "789e0123-e89b-12d3-a456-426614174002",
    "location_name": "Main Fridge",
    "product_variant_id": "012e3456-e89b-12d3-a456-426614174003",
    "product_name": "Organic Milk",
    "product_brand": "Farm Fresh",
    "variant_name": "1L Whole Milk",
    "variant_image_url": "https://example.com/images/milk-1l.jpg",
    "category_name": "Dairy",
    "unit_of_measure_id": "345e6789-e89b-12d3-a456-426614174004",
    "unit_name": "Liter",
    "unit_symbol": "L",
    "quantity": 5.0,
    "status": "fresh"
  },
  "timestamp": "2025-06-12T10:30:00Z",
  "request_id": "req_123456789"
}
```

---

## 3. Core API Endpoints

### Authentication & User

* **Register:**  
 `POST /auth/register`

  Request: `{ username, email, password, confirm_password, ... }`

* **Login:**  
 `POST /auth/login`

  Request: `{ email, password }`

* **Refresh Token:**  
 `POST /auth/refresh`

  Request: `{ refresh_token }`

* **Logout:**  
 `POST /auth/logout`

  Request: `{ refresh_token }`

* **Change Password:**  
 `POST /users/change-password`

  Request: `{ current_password, new_password, confirm_password }`

* **User Profile:**  
 `GET /users/profile`

`PUT /users/profile` (update)

---

### Pantries

* **List/Create Pantries:**  
 `GET /pantries`

 `POST /pantries`

  Query: `page` , `limit` , `owner_only`

* **Pantry Details:**  
 `GET /pantries/{pantryId}`

 `PUT /pantries/{pantryId}`

 `DELETE /pantries/{pantryId}`

* **Membership:**  
`POST /pantries/{pantryId}/members` (invite)  
 `GET /pantries/{pantryId}/members`

`PUT /pantries/{pantryId}/members/{memberId}` (role)  
 `DELETE /pantries/{pantryId}/members/{memberId}`

* **Locations:**  
 `POST /pantries/{pantryId}/locations`

 `GET /pantries/{pantryId}/locations`

* **Expiration Alerts:**  
 `GET /pantries/{pantryId}/expiration/alerts`

 `POST /pantries/{pantryId}/expiration/alerts`

 `GET /expiration/alerts/global`

 `POST /expiration/alerts/global`

* **Expiration Tracking:**  
 `POST /pantries/{pantryId}/expiration/track`

---

### Inventory

* **List/Create Inventory:**
 `GET /pantries/{pantryId}/inventory`

 `POST /pantries/{pantryId}/inventory`

  Query: `page` , `limit` , `search` , `location_id` , `category_id` , `expiring_soon`

* **Advanced Inventory Filtering:**
 `GET /pantries/{pantryId}/inventory/filter`

  **Comprehensive filtering with multiple criteria:**
  + **Text Search:** `query` - Search across product names, variants, brands, and notes
  + **Location Filtering:** `location_id`,  `location_ids[]` - Filter by specific locations
  + **Category Filtering:** `category_id`,  `category_ids[]`,  `include_subcategories` - Filter by categories with optional subcategory inclusion
  + **Product Filtering:** `product_id`,  `product_variant_id`,  `brand` - Filter by specific products or brands
  + **Stock Status:** `stock_status` - Filter by stock levels (`low`,  `out`,  `well_stocked`,  `all`)
  + **Expiration Status:** `expiration_status` - Filter by expiration status (`expired`,  `warning`,  `alert`,  `critical`)
  + **Date Ranges:** `purchase_date_from/to`,  `created_from/to` - Filter by date ranges
  + **Quantity Ranges:** `min_quantity`,  `max_quantity` - Filter by quantity ranges
  + **Sorting:** `sort_by`,  `sort_order` - Sort by various fields (quantity, expiration_date, product_name, etc.)
  + **Pagination:** `page`,  `limit` - Standard pagination support

  **Example Usage:**
  

```
  GET /pantries/{pantryId}/inventory/filter?location_id=uuid&category_id=uuid&include_subcategories=true&stock_status=low&sort_by=expiration_date&sort_order=asc&page=1&limit=20
  ```

* **Inventory Item:**
 `GET /inventory/{itemId}`

 `PUT /inventory/{itemId}`

* **Consume Inventory:**
 `POST /inventory/{itemId}/consume`

  Request: `{ consumed_quantity, notes }`

* **Bulk Operations:**
`POST /pantries/{pantryId}/inventory/bulk` (bulk create)
`PUT /inventory/bulk` (bulk update)
`POST /inventory/bulk/consume` (bulk consume)

---

### Shopping Lists

* **List/Create Shopping Lists:**  
 `GET /pantries/{pantryId}/shopping-lists`

 `POST /pantries/{pantryId}/shopping-lists`

* **Shopping List Details:**  
 `GET /pantries/{pantryId}/shopping-lists/{listId}`

 `PUT /pantries/{pantryId}/shopping-lists/{listId}`

 `DELETE /pantries/{pantryId}/shopping-lists/{listId}`

* **Items:**  
 `POST /pantries/{pantryId}/shopping-lists/{listId}/items`

 `PUT /pantries/{pantryId}/shopping-lists/{listId}/items/{itemId}`

 `DELETE /pantries/{pantryId}/shopping-lists/{listId}/items/{itemId}`

* **Bulk Mark Purchased/Not Purchased:**  
 `PATCH /pantries/{pantryId}/shopping-lists/{listId}/items/bulk-purchased`

 `DELETE /pantries/{pantryId}/shopping-lists/{listId}/items/bulk-purchased`

* **Stats:**  
 `GET /pantries/{pantryId}/shopping-lists/{listId}/stats`

 `GET /pantries/{pantryId}/shopping-lists/stats`

---

### Recipes

* **List/Create Recipes:**  
 `GET /recipes`

 `POST /recipes`

  Query: `page` , `limit` , `search` , `cuisine` , `category` , `difficulty` , etc.

* **Recipe Details:**  
 `GET /recipes/{recipeId}`

 `PUT /recipes/{recipeId}`

 `DELETE /recipes/{recipeId}`

* **Check Inventory for Recipe:**  
 `POST /recipes/{recipeId}/check-inventory`

  Request: `{ pantry_id }`

* **Cook Recipe:**  
 `POST /recipes/{recipeId}/cook`

* **Scale Recipe:**  
 `POST /recipes/{recipeId}/scale`

  Request: `{ servings }`

* **Public/Search:**  
 `GET /recipes/public`

 `GET /recipes/search`

---

### Purchases

* **Create Purchase:**  
 `POST /purchases`

  Request: `{ pantry_id, purchase_date, items, ... }`

* **Get/Delete Purchase:**  
 `GET /purchases/{id}`

 `DELETE /purchases/{id}`

* **List Purchases for Pantry:**  
 `GET /purchases/pantry/{pantry_id}`

  Query: `page` , `limit`

* **Link Purchase to Inventory:**  
 `POST /purchases/{id}/inventory`

  Query: `location_id`

---

### Stores

* **List/Create Stores:**  
 `GET /stores`

 `POST /stores`

  Query: `page` , `limit`

* **Store Details:**  
 `GET /stores/{id}`

 `PUT /stores/{id}`

 `DELETE /stores/{id}`

* **Search Stores:**  
 `GET /stores/search`

  Query: `query`

---

## 4. Enhanced Response Fields

Most resource responses now include human-readable fields for display:
* Inventory: `pantry_name`,  `location_name`,  `product_name`,  `variant_name`,  `category_name`,  `unit_name`, etc.
* Shopping List: item names, units, status, etc.
* Recipes: ingredient names, category, etc.
* Purchases: store name, items, etc.
* Stores: name, address, etc.

---

## 5. Error Handling

* All errors use a generic, secure structure:
  

```json
  {
    "success": false,
    "error": {
      "code": "NOT_FOUND",
      "message": "Resource not found"
    },
    "timestamp": "...",
    "request_id": "..."
  }
  ```

* Error codes: `VALIDATION_FAILED`,  `UNAUTHORIZED`,  `FORBIDDEN`,  `NOT_FOUND`,  `CONFLICT`,  `BUSINESS_RULE_VIOLATION`,  `RATE_LIMIT_EXCEEDED`,  `INTERNAL_SERVER_ERROR`, etc.
* Clients should use `error.code` for programmatic handling and display user-friendly messages.

---

## 6. Pagination, Filtering, and Sorting

* List endpoints support:
  + `page`: Page number (default: 1)
  + `limit`: Items per page (default: 10, max: 100)
  + `sort_by`,  `sort_order`
  + Resource-specific filters (e.g.,  `search`,  `category_id`,  `location_id`, etc.)

Example:

```
GET /pantries/{pantryId}/inventory?page=1&limit=20&search=milk&location_id=uuid&category_id=uuid
```

---

## 7. Idempotency

* For critical operations (e.g., inventory creation, purchase), include:
  

```
  Idempotency-Key: unique-operation-key-123
  ```

* Prevents duplicate processing on retries.

---

## 8. Bulk Operations

* Bulk create/update/consume for inventory and shopping list items:
  + `POST /pantries/{pantryId}/inventory/bulk`
  + `PUT /inventory/bulk`
  + `POST /inventory/bulk/consume`
  + `PATCH/DELETE /pantries/{pantryId}/shopping-lists/{listId}/items/bulk-purchased`

---

## 9. Security and Best Practices

* Always use HTTPS in production.
* Use JWT Bearer tokens for all protected endpoints.
* Handle token refresh and logout securely.
* Validate all inputs on the client before sending.
* Handle errors gracefully and display user-friendly messages.

---

## 10. Example Integration Patterns

* Use React Query or SWR for data fetching and caching.
* Use optimistic updates for offline-first UX.
* Handle error codes and display appropriate UI feedback.
* Use the enhanced response fields for direct UI rendering (no extra lookups needed).

---

**For full request/response schemas and up-to-date endpoint details, always refer to the [Swagger/OpenAPI documentation](https://api.pantrypal.com/docs/) or the latest backend docs.**
