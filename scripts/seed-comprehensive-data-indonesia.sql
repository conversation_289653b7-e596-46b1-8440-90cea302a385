-- Comprehensive Seed Data for Pantry Pal Testing (Indonesian Version)
-- This file contains realistic test data for all major entities and scenarios with Indonesian localization

-- Clear existing data (in dependency order)
DELETE FROM notifications;
DELETE FROM alert_configurations;
DELETE FROM usage_logs;
DELETE FROM inventory_adjustments;
DELETE FROM shopping_list_items;
DELETE FROM shopping_lists;
DELETE FROM recipe_collection_items;
DELETE FROM recipe_collections;
DELETE FROM recipe_reviews;
DELETE FROM recipe_recipe_tags;
DELETE FROM recipe_tags;
DELETE FROM recipe_nutrition;
DELETE FROM recipe_media;
DELETE FROM recipe_instructions;
DELETE FROM recipe_ingredients;
DELETE FROM recipes;
DELETE FROM inventory_items;
DELETE FROM pantry_locations;
DELETE FROM pantry_memberships;
DELETE FROM pantries;
DELETE FROM product_variants;
DELETE FROM products;
DELETE FROM refresh_tokens;
DELETE FROM users;
DELETE FROM stores;

-- ============================================================================
-- USERS - Create Indonesian test users with different roles and scenarios
-- ============================================================================

-- Main test users with different roles and scenarios
INSERT INTO users (id, username, email, password_hash, first_name, last_name, profile_picture_url) VALUES 
    ('11111111-1111-1111-1111-111111111111', 'admin_user', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'Sistem', 'https://example.com/avatars/admin.jpg'),
    ('22222222-2222-2222-2222-222222222222', 'budi_santoso', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Budi', 'Santoso', 'https://example.com/avatars/budi.jpg'),
    ('33333333-3333-3333-3333-333333333333', 'sari_dewi', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sari', 'Dewi', 'https://example.com/avatars/sari.jpg'),
    ('44444444-4444-4444-4444-444444444444', 'agus_wijaya', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Agus', 'Wijaya', 'https://example.com/avatars/agus.jpg'),
    ('55555555-5555-5555-5555-555555555555', 'rina_putri', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Rina', 'Putri', 'https://example.com/avatars/rina.jpg'),
    ('66666666-6666-6666-6666-666666666666', 'chef_indra', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Indra', 'Kusuma', 'https://example.com/avatars/chef.jpg'),
    ('77777777-7777-7777-7777-777777777777', 'ibu_maya', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Maya', 'Sari', 'https://example.com/avatars/ibu.jpg'),
    ('88888888-8888-8888-8888-888888888888', 'pak_joko', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Joko', 'Widodo', 'https://example.com/avatars/pak.jpg');

-- ============================================================================
-- STORES - Create Indonesian stores for purchase history scenarios
-- ============================================================================

INSERT INTO stores (store_id, name, address, city, country, phone_number, website) VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Indomaret', 'Jl. Sudirman No. 123', 'Jakarta', 'Indonesia', '+62-21-5551234', 'https://indomaret.co.id'),
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Alfamart', 'Jl. Thamrin No. 456', 'Jakarta', 'Indonesia', '+62-21-5555678', 'https://alfamart.co.id'),
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Hypermart', 'Mall Kelapa Gading', 'Jakarta', 'Indonesia', '+62-21-4567890', 'https://hypermart.co.id'),
    ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'Carrefour', 'Jl. HR Rasuna Said', 'Jakarta', 'Indonesia', '+62-21-7890123', 'https://carrefour.co.id'),
    ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'Pasar Tradisional Minggu', 'Jl. Raya Pasar Minggu', 'Jakarta', 'Indonesia', '+62-21-2345678', NULL);

-- ============================================================================
-- PANTRIES - Create Indonesian pantries representing different scenarios
-- ============================================================================

-- Personal pantries
INSERT INTO pantries (pantry_id, name, description, owner_user_id) VALUES 
    ('10000000-0000-0000-0000-000000000001', 'Dapur Budi', 'Dapur utama rumah Budi', '22222222-2222-2222-2222-222222222222'),
    ('10000000-0000-0000-0000-000000000002', 'Apartemen Sari', 'Dapur kecil apartemen Sari', '33333333-3333-3333-3333-333333333333'),
    ('10000000-0000-0000-0000-000000000003', 'Kulkas Garasi Agus', 'Penyimpanan tambahan di garasi', '44444444-4444-4444-4444-444444444444'),
    ('10000000-0000-0000-0000-000000000004', 'Dapur Keluarga', 'Dapur utama keluarga Maya dan Joko', '77777777-7777-7777-7777-777777777777'),
    ('10000000-0000-0000-0000-000000000005', 'Dapur Restoran', 'Penyimpanan dapur profesional Chef Indra', '66666666-6666-6666-6666-666666666666'),
    ('10000000-0000-0000-0000-000000000006', 'Pantry Kantor', 'Ruang makan kantor', '11111111-1111-1111-1111-111111111111');

-- ============================================================================
-- PANTRY MEMBERSHIPS - Create realistic membership scenarios
-- ============================================================================

-- Active memberships with different roles
INSERT INTO pantry_memberships (pantry_membership_id, pantry_id, user_id, role, status, invited_by_user_id) VALUES 
    -- Owners (auto-created)
    ('20000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000002', '33333333-3333-3333-3333-333333333333', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000003', '44444444-4444-4444-4444-444444444444', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000004', '77777777-7777-7777-7777-777777777777', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000005', '66666666-6666-6666-6666-666666666666', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000006', '11111111-1111-1111-1111-111111111111', 'owner', 'active', NULL),
    
    -- Family sharing - Pak Joko as admin in family kitchen
    ('20000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000004', '88888888-8888-8888-8888-888888888888', 'admin', 'active', '77777777-7777-7777-7777-777777777777'),
    
    -- Restaurant staff - Rina as editor in restaurant
    ('20000000-0000-0000-0000-000000000008', '10000000-0000-0000-0000-000000000005', '55555555-5555-5555-5555-555555555555', 'editor', 'active', '66666666-6666-6666-6666-666666666666'),
    
    -- Office access - Sari as viewer in office pantry
    ('20000000-0000-0000-0000-000000000009', '10000000-0000-0000-0000-000000000006', '33333333-3333-3333-3333-333333333333', 'viewer', 'active', '11111111-1111-1111-1111-111111111111');

-- Pending invitations for testing
INSERT INTO pantry_memberships (pantry_membership_id, pantry_id, user_id, role, status, invited_by_user_id) VALUES 
    ('20000000-0000-0000-0000-000000000010', '10000000-0000-0000-0000-000000000002', '44444444-4444-4444-4444-444444444444', 'viewer', 'pending_invitation', '33333333-3333-3333-3333-333333333333'),
    ('20000000-0000-0000-0000-000000000011', '10000000-0000-0000-0000-000000000006', '22222222-2222-2222-2222-222222222222', 'editor', 'pending_invitation', '11111111-1111-1111-1111-111111111111');

-- ============================================================================
-- PANTRY LOCATIONS - Create realistic Indonesian storage locations
-- ============================================================================

-- Dapur Budi locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', 'Lemari Dapur', 'Lemari penyimpanan utama'),
    ('30000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', 'Kulkas', 'Kulkas 2 pintu Samsung'),
    ('30000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', 'Freezer', 'Freezer box di ruang belakang'),
    ('30000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001', 'Rak Bumbu', 'Rak khusus bumbu dan rempah'),
    ('30000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000001', 'Meja Dapur', 'Laci penyimpanan di meja dapur');

-- Apartemen Sari locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000002', 'Lemari Kecil', 'Lemari dapur apartemen'),
    ('30000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000002', 'Kulkas Mini', 'Kulkas 1 pintu'),
    ('30000000-0000-0000-0000-000000000008', '10000000-0000-0000-0000-000000000002', 'Rak Dinding', 'Rak gantung di dinding');

-- Kulkas Garasi Agus locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000009', '10000000-0000-0000-0000-000000000003', 'Kulkas Garasi', 'Kulkas bekas di garasi'),
    ('30000000-0000-0000-0000-000000000010', '10000000-0000-0000-0000-000000000003', 'Rak Besi', 'Rak besi untuk penyimpanan kering');

-- Dapur Keluarga locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000011', '10000000-0000-0000-0000-000000000004', 'Lemari Utama', 'Lemari dapur keluarga besar'),
    ('30000000-0000-0000-0000-000000000012', '10000000-0000-0000-0000-000000000004', 'Kulkas Keluarga', 'Kulkas side by side'),
    ('30000000-0000-0000-0000-000000000013', '10000000-0000-0000-0000-000000000004', 'Dispenser', 'Area sekitar dispenser'),
    ('30000000-0000-0000-0000-000000000014', '10000000-0000-0000-0000-000000000004', 'Rak Snack', 'Rak khusus cemilan anak');

-- Dapur Restoran locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000015', '10000000-0000-0000-0000-000000000005', 'Cold Storage', 'Ruang pendingin utama'),
    ('30000000-0000-0000-0000-000000000016', '10000000-0000-0000-0000-000000000005', 'Dry Storage', 'Gudang bahan kering'),
    ('30000000-0000-0000-0000-000000000017', '10000000-0000-0000-0000-000000000005', 'Freezer Room', 'Ruang beku'),
    ('30000000-0000-0000-0000-000000000018', '10000000-0000-0000-0000-000000000005', 'Prep Station', 'Area persiapan masak');

-- Pantry Kantor locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES
    ('30000000-0000-0000-0000-000000000019', '10000000-0000-0000-0000-000000000006', 'Kulkas Kantor', 'Kulkas bersama karyawan'),
    ('30000000-0000-0000-0000-000000000020', '10000000-0000-0000-0000-000000000006', 'Lemari Snack', 'Lemari cemilan kantor');

-- ============================================================================
-- PRODUCTS - Create comprehensive Indonesian product catalog
-- ============================================================================

-- Get category IDs for reference
-- Dairy & Eggs products (Indonesian)
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000001', 'Susu Segar', 'Susu segar full cream', category_id, 'Ultra Milk'
FROM categories WHERE name = 'Dairy & Eggs';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000002', 'Telur Ayam', 'Telur ayam kampung grade A', category_id, 'Telur Bahagia'
FROM categories WHERE name = 'Dairy & Eggs';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000003', 'Keju Cheddar', 'Keju cheddar olahan', category_id, 'Kraft'
FROM categories WHERE name = 'Dairy & Eggs';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000004', 'Yogurt Yunani', 'Yogurt yunani plain', category_id, 'Heavenly Blush'
FROM categories WHERE name = 'Dairy & Eggs';

-- Meat & Seafood products (Indonesian)
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000005', 'Dada Ayam', 'Dada ayam tanpa tulang dan kulit', category_id, 'So Good'
FROM categories WHERE name = 'Meat & Seafood';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000006', 'Daging Sapi Giling', 'Daging sapi giling segar', category_id, 'Santori'
FROM categories WHERE name = 'Meat & Seafood';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000007', 'Ikan Salmon', 'Fillet ikan salmon segar', category_id, 'Salmon Indonesia'
FROM categories WHERE name = 'Meat & Seafood';

-- Fruits & Vegetables products (Indonesian)
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000008', 'Pisang Cavendish', 'Pisang cavendish segar', category_id, 'Sunpride'
FROM categories WHERE name = 'Fruits & Vegetables';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000009', 'Apel Malang', 'Apel malang manis', category_id, 'Kusuma Agro'
FROM categories WHERE name = 'Fruits & Vegetables';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000010', 'Bayam Segar', 'Bayam hijau segar', category_id, 'Sayur Sehat'
FROM categories WHERE name = 'Fruits & Vegetables';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000011', 'Tomat Merah', 'Tomat merah segar', category_id, 'Tomat Nusantara'
FROM categories WHERE name = 'Fruits & Vegetables';

-- Pantry Staples products (Indonesian)
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000012', 'Minyak Goreng', 'Minyak goreng kelapa sawit', category_id, 'Bimoli'
FROM categories WHERE name = 'Pantry Staples';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000013', 'Mie Spaghetti', 'Pasta spaghetti', category_id, 'La Fonte'
FROM categories WHERE name = 'Pantry Staples';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000014', 'Beras Putih', 'Beras putih premium', category_id, 'Ramos'
FROM categories WHERE name = 'Pantry Staples';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000015', 'Kacang Hitam', 'Kacang hitam kaleng', category_id, 'ABC'
FROM categories WHERE name = 'Pantry Staples';

-- Beverages products (Indonesian)
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000016', 'Kopi Bubuk', 'Kopi bubuk robusta', category_id, 'Kapal Api'
FROM categories WHERE name = 'Beverages';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000017', 'Jus Jeruk', 'Jus jeruk segar', category_id, 'Minute Maid'
FROM categories WHERE name = 'Beverages';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000018', 'Air Mineral', 'Air mineral dalam kemasan', category_id, 'Aqua'
FROM categories WHERE name = 'Beverages';

-- Snacks & Sweets products (Indonesian)
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000019', 'Kacang Almond', 'Kacang almond panggang', category_id, 'Dua Kelinci'
FROM categories WHERE name = 'Snacks & Sweets';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000020', 'Cokelat Hitam', 'Cokelat hitam 70%', category_id, 'Monggo'
FROM categories WHERE name = 'Snacks & Sweets';

-- ============================================================================
-- PRODUCT VARIANTS - Create specific Indonesian product variants with barcodes
-- ============================================================================

-- Dairy & Eggs variants (Indonesian)
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000001', '40000000-0000-0000-0000-000000000001', 'Ultra Milk 1 Liter', 'Kemasan tetra pak 1 liter', '8992761001234', 'single', unit_id
FROM units_of_measure WHERE symbol = 'L';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000002', '40000000-0000-0000-0000-000000000002', 'Telur Ayam 12 Butir', 'Kemasan plastik 12 butir', '8992761002345', 'single', unit_id
FROM units_of_measure WHERE symbol = 'dz';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000003', '40000000-0000-0000-0000-000000000003', 'Keju Cheddar 200g', 'Kemasan blok 200 gram', '8992761003456', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000004', '40000000-0000-0000-0000-000000000004', 'Yogurt Yunani 150g', 'Cup plastik 150 gram', '8992761004567', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

-- Meat & Seafood variants (Indonesian)
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000005', '40000000-0000-0000-0000-000000000005', 'Dada Ayam 500g', 'Kemasan styrofoam 500 gram', '8992761005678', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000006', '40000000-0000-0000-0000-000000000006', 'Daging Sapi Giling 250g', 'Kemasan plastik 250 gram', '8992761006789', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000007', '40000000-0000-0000-0000-000000000007', 'Ikan Salmon 300g', 'Fillet salmon 300 gram', '8992761007890', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

-- Fruits & Vegetables variants (Indonesian)
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000008', '40000000-0000-0000-0000-000000000008', 'Pisang Cavendish 1kg', 'Sisir pisang 1 kilogram', '8992761008901', 'single', unit_id
FROM units_of_measure WHERE symbol = 'kg';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000009', '40000000-0000-0000-0000-000000000009', 'Apel Malang 500g', 'Kantong plastik 500 gram', '8992761009012', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000010', '40000000-0000-0000-0000-000000000010', 'Bayam Segar 250g', 'Ikat bayam 250 gram', '8992761010123', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

-- Pantry Staples variants (Indonesian)
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000011', '40000000-0000-0000-0000-000000000012', 'Bimoli 1 Liter', 'Botol plastik 1 liter', '8992761011234', 'single', unit_id
FROM units_of_measure WHERE symbol = 'L';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000012', '40000000-0000-0000-0000-000000000013', 'La Fonte Spaghetti 500g', 'Kemasan karton 500 gram', '8992761012345', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000013', '40000000-0000-0000-0000-000000000014', 'Beras Ramos 5kg', 'Karung plastik 5 kilogram', '8992761013456', 'single', unit_id
FROM units_of_measure WHERE symbol = 'kg';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000014', '40000000-0000-0000-0000-000000000015', 'ABC Kacang Hitam 400g', 'Kaleng 400 gram', '8992761014567', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

-- Beverages variants (Indonesian)
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000015', '40000000-0000-0000-0000-000000000016', 'Kapal Api 200g', 'Kemasan sachet 200 gram', '8992761015678', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000016', '40000000-0000-0000-0000-000000000017', 'Minute Maid 1 Liter', 'Kemasan tetra pak 1 liter', '8992761016789', 'single', unit_id
FROM units_of_measure WHERE symbol = 'L';

-- Snacks variants (Indonesian)
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000017', '40000000-0000-0000-0000-000000000019', 'Dua Kelinci Almond 150g', 'Kemasan plastik 150 gram', '8992761017890', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000018', '40000000-0000-0000-0000-000000000020', 'Monggo Cokelat 100g', 'Batang cokelat 100 gram', '8992761018901', 'single', unit_id
FROM units_of_measure WHERE symbol = 'g';
