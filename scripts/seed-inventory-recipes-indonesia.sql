-- Indonesian Inventory and Recipe Seed Data for Pantry Pal Testing
-- This file contains Indonesian localized inventory items, recipes, and related data

-- ============================================================================
-- INVENTORY ITEMS - Create realistic Indonesian inventory across different pantries
-- ============================================================================

-- Dapur Budi inventory
INSERT INTO inventory_items (item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes)
VALUES
    ('60000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000001', 0.75, (SELECT unit_id FROM units_of_measure WHERE symbol = 'L'), '30000000-0000-0000-0000-000000000002', '2025-06-20', '2025-06-10', 15000, 'Sudah dibuka 3 hari lalu'),
    ('60000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000002', 8, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), '30000000-0000-0000-0000-000000000002', '2025-06-25', '2025-06-08', 25000, 'Sudah pakai 4 butir'),
    ('60000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000003', 150, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), '30000000-0000-0000-0000-000000000002', '2025-07-15', '2025-06-05', 35000, 'Masih sekitar 3/4'),
    ('60000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000005', 400, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), '30000000-0000-0000-0000-000000000003', '2025-06-18', '2025-06-12', 45000, 'Dada ayam beku'),
    ('60000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000011', 800, (SELECT unit_id FROM units_of_measure WHERE symbol = 'mL'), '30000000-0000-0000-0000-000000000001', '2026-12-31', '2025-05-15', 28000, 'Minyak goreng premium'),
    ('60000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000012', 400, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), '30000000-0000-0000-0000-000000000001', '2026-03-01', '2025-04-20', 12000, 'Kemasan sudah dibuka'),
    ('60000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000015', 150, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), '30000000-0000-0000-0000-000000000001', '2027-01-01', '2025-06-01', 45000, 'Kopi bubuk Kapal Api');

-- Dapur Keluarga inventory
INSERT INTO inventory_items (item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes)
VALUES
    ('60000000-0000-0000-0000-000000000008', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000001', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'L'), '30000000-0000-0000-0000-000000000012', '2025-06-22', '2025-06-12', 15000, 'Susu segar 1 liter penuh'),
    ('60000000-0000-0000-0000-000000000009', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000002', 12, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), '30000000-0000-0000-0000-000000000012', '2025-06-28', '2025-06-10', 25000, 'Telur ayam 1 lusin penuh'),
    ('60000000-0000-0000-0000-000000000010', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000008', 800, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), '30000000-0000-0000-0000-000000000011', '2025-06-16', '2025-06-13', 18000, 'Pisang matang'),
    ('60000000-0000-0000-0000-000000000011', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000009', 450, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), '30000000-0000-0000-0000-000000000012', '2025-06-20', '2025-06-11', 22000, 'Apel Malang segar'),
    ('60000000-0000-0000-0000-000000000012', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000013', 4, (SELECT unit_id FROM units_of_measure WHERE symbol = 'kg'), '30000000-0000-0000-0000-000000000011', '2026-08-01', '2025-05-20', 65000, 'Beras putih premium'),
    ('60000000-0000-0000-0000-000000000013', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000014', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'can'), '30000000-0000-0000-0000-000000000011', '2026-12-01', '2025-06-05', 18000, 'Kacang hitam kaleng'),
    ('60000000-0000-0000-0000-000000000014', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000017', 100, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), '30000000-0000-0000-0000-000000000014', '2025-12-01', '2025-06-01', 35000, 'Cemilan almond untuk anak');

-- Dapur Restoran inventory (larger quantities)
INSERT INTO inventory_items (item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes)
VALUES
    ('60000000-0000-0000-0000-000000000015', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000005', 10, (SELECT unit_id FROM units_of_measure WHERE symbol = 'kg'), '30000000-0000-0000-0000-000000000017', '2025-06-20', '2025-06-10', 450000, 'Dada ayam untuk restoran'),
    ('60000000-0000-0000-0000-000000000016', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000006', 5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'kg'), '30000000-0000-0000-0000-000000000017', '2025-06-18', '2025-06-12', 350000, 'Daging sapi giling'),
    ('60000000-0000-0000-0000-000000000017', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000007', 3, (SELECT unit_id FROM units_of_measure WHERE symbol = 'kg'), '30000000-0000-0000-0000-000000000017', '2025-06-17', '2025-06-13', 900000, 'Fillet salmon segar'),
    ('60000000-0000-0000-0000-000000000018', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000011', 5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'L'), '30000000-0000-0000-0000-000000000016', '2026-12-31', '2025-05-01', 140000, 'Minyak goreng curah'),
    ('60000000-0000-0000-0000-000000000019', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000012', 5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'kg'), '30000000-0000-0000-0000-000000000016', '2026-06-01', '2025-04-15', 60000, 'Pasta spaghetti curah'),
    ('60000000-0000-0000-0000-000000000020', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000013', 25, (SELECT unit_id FROM units_of_measure WHERE symbol = 'kg'), '30000000-0000-0000-0000-000000000016', '2026-10-01', '2025-03-20', 325000, 'Beras untuk restoran');

-- Pantry Kantor inventory
INSERT INTO inventory_items (item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes)
VALUES
    ('60000000-0000-0000-0000-000000000021', '10000000-0000-0000-0000-000000000006', '50000000-0000-0000-0000-000000000015', 150, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), '30000000-0000-0000-0000-000000000020', '2026-08-01', '2025-06-01', 45000, 'Kopi kantor'),
    ('60000000-0000-0000-0000-000000000022', '10000000-0000-0000-0000-000000000006', '50000000-0000-0000-0000-000000000017', 100, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), '30000000-0000-0000-0000-000000000020', '2025-10-01', '2025-06-05', 35000, 'Cemilan almond kantor');

-- ============================================================================
-- RECIPE TAGS - Create Indonesian recipe categorization tags
-- ============================================================================

INSERT INTO recipe_tags (id, name, description) VALUES
    ('70000000-0000-0000-0000-000000000001', 'Cepat & Mudah', 'Resep yang bisa dibuat dalam 30 menit atau kurang'),
    ('70000000-0000-0000-0000-000000000002', 'Vegetarian', 'Resep ramah vegetarian'),
    ('70000000-0000-0000-0000-000000000003', 'Sehat', 'Resep bergizi dan seimbang'),
    ('70000000-0000-0000-0000-000000000004', 'Ramah Keluarga', 'Resep yang disukai anak-anak'),
    ('70000000-0000-0000-0000-000000000005', 'Makanan Rumahan', 'Hidangan yang mengenyangkan dan memuaskan'),
    ('70000000-0000-0000-0000-000000000006', 'Rendah Karbo', 'Resep rendah karbohidrat'),
    ('70000000-0000-0000-0000-000000000007', 'Bebas Gluten', 'Resep bebas gluten'),
    ('70000000-0000-0000-0000-000000000008', 'Sarapan', 'Resep untuk makan pagi'),
    ('70000000-0000-0000-0000-000000000009', 'Makan Malam', 'Resep untuk makan malam'),
    ('70000000-0000-0000-0000-000000000010', 'Dessert', 'Makanan penutup dan camilan manis');

-- ============================================================================
-- RECIPES - Create comprehensive Indonesian recipe collection
-- ============================================================================

-- Recipe 1: Telur Orak-Arik (Budi's recipe)
INSERT INTO recipes (id, user_id, title, description, difficulty, prep_time, cook_time, total_time, servings, is_public)
VALUES ('80000000-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', 'Telur Orak-Arik Sempurna', 'Telur orak-arik yang lembut dan gurih', 'easy', 5, 5, 10, 2, true);

-- Recipe 2: Tumis Ayam Sayur (Sari's recipe)
INSERT INTO recipes (id, user_id, title, description, difficulty, prep_time, cook_time, total_time, servings, is_public)
VALUES ('80000000-0000-0000-0000-000000000002', '33333333-3333-3333-3333-333333333333', 'Tumis Ayam Sayur Sehat', 'Tumisan ayam dengan sayuran segar', 'medium', 15, 10, 25, 4, true);

-- Recipe 3: Mie Aglio Olio (Chef Indra's recipe)
INSERT INTO recipes (id, user_id, title, description, difficulty, prep_time, cook_time, total_time, servings, is_public)
VALUES ('80000000-0000-0000-0000-000000000003', '66666666-6666-6666-6666-666666666666', 'Mie Aglio Olio ala Indonesia', 'Pasta aglio olio dengan sentuhan Indonesia', 'easy', 10, 15, 25, 4, true);

-- Recipe 4: Nasi Campur Keluarga (Family recipe)
INSERT INTO recipes (id, user_id, title, description, difficulty, prep_time, cook_time, total_time, servings, is_public)
VALUES ('80000000-0000-0000-0000-000000000004', '77777777-7777-7777-7777-777777777777', 'Nasi Campur Keluarga', 'Nasi campur bergizi dengan sayur dan protein', 'medium', 20, 25, 45, 6, false);

-- Recipe 5: Cokelat Almond Panggang (Rina's dessert)
INSERT INTO recipes (id, user_id, title, description, difficulty, prep_time, cook_time, total_time, servings, is_public)
VALUES ('80000000-0000-0000-0000-000000000005', '55555555-5555-5555-5555-555555555555', 'Cokelat Almond Panggang', 'Camilan cokelat almond yang mudah dibuat', 'easy', 10, 0, 10, 8, true);

-- ============================================================================
-- RECIPE INGREDIENTS - Add Indonesian ingredients for each recipe
-- ============================================================================

-- Telur Orak-Arik ingredients
INSERT INTO recipe_ingredients (id, recipe_id, product_variant_id, name, quantity, unit_of_measure_id, notes, is_optional, "order")
VALUES
    ('81000000-0000-0000-0000-000000000001', '80000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000002', 'Telur ayam segar', 4, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Telur ayam segar', false, 1),
    ('81000000-0000-0000-0000-000000000002', '80000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000001', 'Susu segar', 50, (SELECT unit_id FROM units_of_measure WHERE symbol = 'mL'), 'Untuk kelembutan', false, 2);

-- Add free text ingredients for telur orak-arik
INSERT INTO recipe_ingredients (id, recipe_id, name, quantity, unit_of_measure_id, notes, is_optional, "order")
VALUES
    ('81000000-0000-0000-0000-000000000003', '80000000-0000-0000-0000-000000000001', 'Mentega', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), '2 sendok makan', false, 3),
    ('81000000-0000-0000-0000-000000000004', '80000000-0000-0000-0000-000000000001', 'Garam', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Sejumput garam', false, 4),
    ('81000000-0000-0000-0000-000000000005', '80000000-0000-0000-0000-000000000001', 'Merica Hitam', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Secukupnya', true, 5);

-- Tumis Ayam Sayur ingredients
INSERT INTO recipe_ingredients (id, recipe_id, product_variant_id, name, quantity, unit_of_measure_id, notes, is_optional, "order")
VALUES
    ('81000000-0000-0000-0000-000000000006', '80000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000005', 'Dada ayam', 400, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), 'Potong memanjang', false, 1),
    ('81000000-0000-0000-0000-000000000007', '80000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000011', 'Minyak goreng', 30, (SELECT unit_id FROM units_of_measure WHERE symbol = 'mL'), 'Untuk menumis', false, 2);

INSERT INTO recipe_ingredients (id, recipe_id, name, quantity, unit_of_measure_id, notes, is_optional, "order")
VALUES
    ('81000000-0000-0000-0000-000000000008', '80000000-0000-0000-0000-000000000002', 'Paprika', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Warna campur', false, 3),
    ('81000000-0000-0000-0000-000000000009', '80000000-0000-0000-0000-000000000002', 'Kecap Manis', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Sendok makan', false, 4),
    ('81000000-0000-0000-0000-000000000010', '80000000-0000-0000-0000-000000000002', 'Bawang Putih', 3, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Siung, cincang halus', false, 5);

-- Mie Aglio Olio ingredients
INSERT INTO recipe_ingredients (id, recipe_id, product_variant_id, name, quantity, unit_of_measure_id, notes, is_optional, "order")
VALUES
    ('81000000-0000-0000-0000-000000000011', '80000000-0000-0000-0000-000000000003', '50000000-0000-0000-0000-000000000012', 'Mie spaghetti', 400, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), 'Mie spaghetti', false, 1),
    ('81000000-0000-0000-0000-000000000012', '80000000-0000-0000-0000-000000000003', '50000000-0000-0000-0000-000000000011', 'Minyak goreng', 60, (SELECT unit_id FROM units_of_measure WHERE symbol = 'mL'), 'Minyak goreng berkualitas', false, 2);

INSERT INTO recipe_ingredients (id, recipe_id, name, quantity, unit_of_measure_id, notes, is_optional, "order")
VALUES
    ('81000000-0000-0000-0000-000000000013', '80000000-0000-0000-0000-000000000003', 'Bawang Putih', 6, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Siung, iris tipis', false, 3),
    ('81000000-0000-0000-0000-000000000014', '80000000-0000-0000-0000-000000000003', 'Cabai Rawit', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Sesuai selera', true, 4),
    ('81000000-0000-0000-0000-000000000015', '80000000-0000-0000-0000-000000000003', 'Daun Peterseli', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Segar, cincang', false, 5);

-- Nasi Campur Keluarga ingredients
INSERT INTO recipe_ingredients (id, recipe_id, product_variant_id, name, quantity, unit_of_measure_id, notes, is_optional, "order")
VALUES
    ('81000000-0000-0000-0000-000000000016', '80000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000013', 'Beras putih', 500, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), 'Beras putih premium', false, 1),
    ('81000000-0000-0000-0000-000000000017', '80000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000014', 'Kacang hitam', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'can'), 'Kacang hitam', false, 2),
    ('81000000-0000-0000-0000-000000000018', '80000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000005', 'Dada ayam', 300, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), 'Ayam panggang', false, 3);

-- Cokelat Almond Panggang ingredients
INSERT INTO recipe_ingredients (id, recipe_id, product_variant_id, name, quantity, unit_of_measure_id, notes, is_optional, "order")
VALUES
    ('81000000-0000-0000-0000-000000000019', '80000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000018', 'Cokelat hitam', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Batang cokelat hitam', false, 1),
    ('81000000-0000-0000-0000-000000000020', '80000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000017', 'Kacang almond', 100, (SELECT unit_id FROM units_of_measure WHERE symbol = 'g'), 'Almond cincang', false, 2);

-- ============================================================================
-- RECIPE INSTRUCTIONS - Add step-by-step Indonesian instructions
-- ============================================================================

-- Telur Orak-Arik instructions
INSERT INTO recipe_instructions (id, recipe_id, step_number, instruction, duration)
VALUES
    ('82000000-0000-0000-0000-000000000001', '80000000-0000-0000-0000-000000000001', 1, 'Pecahkan telur ke dalam mangkuk dan kocok bersama susu, garam, dan merica hingga rata.', 2),
    ('82000000-0000-0000-0000-000000000002', '80000000-0000-0000-0000-000000000001', 2, 'Panaskan mentega di wajan anti lengket dengan api sedang-kecil.', 1),
    ('82000000-0000-0000-0000-000000000003', '80000000-0000-0000-0000-000000000001', 3, 'Tuang campuran telur ke dalam wajan dan diamkan selama 20 detik.', 1),
    ('82000000-0000-0000-0000-000000000004', '80000000-0000-0000-0000-000000000001', 4, 'Aduk perlahan dengan spatula, dorong telur dari pinggir ke tengah. Ulangi hingga telur matang.', 3),
    ('82000000-0000-0000-0000-000000000005', '80000000-0000-0000-0000-000000000001', 5, 'Angkat dari api saat telur masih sedikit basah. Telur akan terus matang. Sajikan segera.', 1);

-- Tumis Ayam Sayur instructions
INSERT INTO recipe_instructions (id, recipe_id, step_number, instruction, duration)
VALUES
    ('82000000-0000-0000-0000-000000000006', '80000000-0000-0000-0000-000000000002', 1, 'Potong dada ayam memanjang dan bumbui dengan garam dan merica.', 5),
    ('82000000-0000-0000-0000-000000000007', '80000000-0000-0000-0000-000000000002', 2, 'Iris paprika dan cincang halus bawang putih. Siapkan semua sayuran.', 8),
    ('82000000-0000-0000-0000-000000000008', '80000000-0000-0000-0000-000000000002', 3, 'Panaskan minyak goreng di wajan besar atau wok dengan api besar.', 2),
    ('82000000-0000-0000-0000-000000000009', '80000000-0000-0000-0000-000000000002', 4, 'Masukkan potongan ayam dan masak hingga kecokelatan, sekitar 4-5 menit.', 5),
    ('82000000-0000-0000-0000-000000000010', '80000000-0000-0000-0000-000000000002', 5, 'Tambahkan sayuran dan bawang putih, tumis selama 3-4 menit hingga sayur layu.', 4),
    ('82000000-0000-0000-0000-000000000011', '80000000-0000-0000-0000-000000000002', 6, 'Tambahkan kecap manis dan aduk rata. Masak 1 menit lagi dan sajikan.', 1);

-- Mie Aglio Olio instructions
INSERT INTO recipe_instructions (id, recipe_id, step_number, instruction, duration)
VALUES
    ('82000000-0000-0000-0000-000000000012', '80000000-0000-0000-0000-000000000003', 1, 'Rebus mie spaghetti dalam air mendidih dengan garam hingga al dente sesuai petunjuk kemasan.', 10),
    ('82000000-0000-0000-0000-000000000013', '80000000-0000-0000-0000-000000000003', 2, 'Sementara itu, panaskan minyak goreng di wajan besar dengan api sedang.', 2),
    ('82000000-0000-0000-0000-000000000014', '80000000-0000-0000-0000-000000000003', 3, 'Masukkan irisan bawang putih dan cabai rawit, tumis hingga harum dan kecokelatan.', 3),
    ('82000000-0000-0000-0000-000000000015', '80000000-0000-0000-0000-000000000003', 4, 'Tiriskan mie dan masukkan ke dalam wajan. Aduk rata dengan bumbu.', 2),
    ('82000000-0000-0000-0000-000000000016', '80000000-0000-0000-0000-000000000003', 5, 'Tambahkan daun peterseli cincang, aduk sebentar dan sajikan hangat.', 1);

-- Nasi Campur Keluarga instructions
INSERT INTO recipe_instructions (id, recipe_id, step_number, instruction, duration)
VALUES
    ('82000000-0000-0000-0000-000000000017', '80000000-0000-0000-0000-000000000004', 1, 'Cuci beras hingga bersih dan masak dengan rice cooker atau panci hingga pulen.', 20),
    ('82000000-0000-0000-0000-000000000018', '80000000-0000-0000-0000-000000000004', 2, 'Panaskan kacang hitam kaleng dalam panci kecil dengan sedikit air.', 5),
    ('82000000-0000-0000-0000-000000000019', '80000000-0000-0000-0000-000000000004', 3, 'Panggang atau goreng dada ayam hingga matang dan potong-potong.', 15),
    ('82000000-0000-0000-0000-000000000020', '80000000-0000-0000-0000-000000000004', 4, 'Siapkan sayuran segar seperti timun, tomat, dan selada.', 5),
    ('82000000-0000-0000-0000-000000000021', '80000000-0000-0000-0000-000000000004', 5, 'Tata nasi di piring, tambahkan ayam, kacang hitam, dan sayuran. Sajikan.', 3);

-- Cokelat Almond Panggang instructions
INSERT INTO recipe_instructions (id, recipe_id, step_number, instruction, duration)
VALUES
    ('82000000-0000-0000-0000-000000000022', '80000000-0000-0000-0000-000000000005', 1, 'Lelehkan cokelat hitam dengan cara di-tim atau microwave hingga meleleh sempurna.', 5),
    ('82000000-0000-0000-0000-000000000023', '80000000-0000-0000-0000-000000000005', 2, 'Campurkan almond cincang ke dalam cokelat leleh, aduk rata.', 2),
    ('82000000-0000-0000-0000-000000000024', '80000000-0000-0000-0000-000000000005', 3, 'Tuang campuran ke atas loyang yang dialasi kertas roti, ratakan.', 2),
    ('82000000-0000-0000-0000-000000000025', '80000000-0000-0000-0000-000000000005', 4, 'Dinginkan di kulkas selama 30 menit hingga mengeras, lalu potong-potong.', 1);

-- ============================================================================
-- RECIPE TAGS ASSOCIATIONS - Link recipes with appropriate Indonesian tags
-- ============================================================================

-- Telur Orak-Arik tags
INSERT INTO recipe_recipe_tags (recipe_id, recipe_tag_id) VALUES
    ('80000000-0000-0000-0000-000000000001', '70000000-0000-0000-0000-000000000001'), -- Cepat & Mudah
    ('80000000-0000-0000-0000-000000000001', '70000000-0000-0000-0000-000000000008'); -- Sarapan

-- Tumis Ayam Sayur tags
INSERT INTO recipe_recipe_tags (recipe_id, recipe_tag_id) VALUES
    ('80000000-0000-0000-0000-000000000002', '70000000-0000-0000-0000-000000000003'), -- Sehat
    ('80000000-0000-0000-0000-000000000002', '70000000-0000-0000-0000-000000000004'), -- Ramah Keluarga
    ('80000000-0000-0000-0000-000000000002', '70000000-0000-0000-0000-000000000009'); -- Makan Malam

-- Mie Aglio Olio tags
INSERT INTO recipe_recipe_tags (recipe_id, recipe_tag_id) VALUES
    ('80000000-0000-0000-0000-000000000003', '70000000-0000-0000-0000-000000000001'), -- Cepat & Mudah
    ('80000000-0000-0000-0000-000000000003', '70000000-0000-0000-0000-000000000002'); -- Vegetarian

-- Nasi Campur Keluarga tags
INSERT INTO recipe_recipe_tags (recipe_id, recipe_tag_id) VALUES
    ('80000000-0000-0000-0000-000000000004', '70000000-0000-0000-0000-000000000003'), -- Sehat
    ('80000000-0000-0000-0000-000000000004', '70000000-0000-0000-0000-000000000004'), -- Ramah Keluarga
    ('80000000-0000-0000-0000-000000000004', '70000000-0000-0000-0000-000000000005'); -- Makanan Rumahan

-- Cokelat Almond Panggang tags
INSERT INTO recipe_recipe_tags (recipe_id, recipe_tag_id) VALUES
    ('80000000-0000-0000-0000-000000000005', '70000000-0000-0000-0000-000000000001'), -- Cepat & Mudah
    ('80000000-0000-0000-0000-000000000005', '70000000-0000-0000-0000-000000000010'); -- Dessert
