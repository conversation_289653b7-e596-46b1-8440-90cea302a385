package postgres

import (
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// InventoryItemRepository implements domain.InventoryItemRepository using GORM
type InventoryItemRepository struct {
	db *gorm.DB
}

// NewInventoryItemRepository creates a new inventory item repository
func NewInventoryItemRepository(db *gorm.DB) *InventoryItemRepository {
	return &InventoryItemRepository{db: db}
}

// Create creates a new inventory item
func (r *InventoryItemRepository) Create(item *domain.InventoryItem) error {
	model := &InventoryItemModel{}
	model.FromDomain(item)

	if err := r.db.Create(model).Error; err != nil {
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create inventory item")
	}

	// Update the domain object with generated values
	*item = *model.ToDomain()

	return nil
}

// GetByID retrieves an inventory item by ID
func (r *InventoryItemRepository) GetByID(id uuid.UUID) (*domain.InventoryItem, error) {
	var model InventoryItemModel

	if err := r.db.Where("item_id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "inventory item not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory item by ID")
	}

	return model.ToDomain(), nil
}

// GetByIDWithRelations retrieves an inventory item by ID with all related data
func (r *InventoryItemRepository) GetByIDWithRelations(id uuid.UUID) (*domain.InventoryItemWithRelations, error) {
	var result struct {
		InventoryItemModel
		PantryName      string  `gorm:"column:pantry_name"`
		LocationName    *string `gorm:"column:location_name"`
		ProductName     string  `gorm:"column:product_name"`
		ProductBrand    *string `gorm:"column:product_brand"`
		VariantName     string  `gorm:"column:variant_name"`
		VariantImageURL *string `gorm:"column:variant_image_url"`
		CategoryName    string  `gorm:"column:category_name"`
		UnitName        string  `gorm:"column:unit_name"`
		UnitSymbol      string  `gorm:"column:unit_symbol"`
	}

	query := r.db.Table("inventory_items i").
		Select(`i.*,
			p.name as pantry_name,
			pl.name as location_name,
			pr.name as product_name,
			pr.brand as product_brand,
			pv.name as variant_name,
			pv.image_url as variant_image_url,
			c.name as category_name,
			u.name as unit_name,
			u.symbol as unit_symbol`).
		Joins("JOIN pantries p ON i.pantry_id = p.pantry_id").
		Joins("LEFT JOIN pantry_locations pl ON i.location_id = pl.pantry_location_id").
		Joins("JOIN product_variants pv ON i.product_variant_id = pv.variant_id").
		Joins("JOIN products pr ON pv.product_id = pr.product_id").
		Joins("JOIN categories c ON pr.category_id = c.category_id").
		Joins("JOIN units_of_measure u ON i.unit_of_measure_id = u.unit_id").
		Where("i.item_id = ?", id)

	if err := query.First(&result).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "inventory item not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory item with relations")
	}

	return &domain.InventoryItemWithRelations{
		InventoryItem:   result.InventoryItemModel.ToDomain(),
		PantryName:      result.PantryName,
		LocationName:    result.LocationName,
		ProductName:     result.ProductName,
		ProductBrand:    result.ProductBrand,
		VariantName:     result.VariantName,
		VariantImageURL: result.VariantImageURL,
		CategoryName:    result.CategoryName,
		UnitName:        result.UnitName,
		UnitSymbol:      result.UnitSymbol,
	}, nil
}

// GetByPantryID retrieves inventory items by pantry ID with pagination
func (r *InventoryItemRepository) GetByPantryID(pantryID uuid.UUID, page, limit int) ([]*domain.InventoryItem, int64, error) {
	var models []InventoryItemModel
	var total int64

	// Count total records
	if err := r.db.Model(&InventoryItemModel{}).
		Where("pantry_id = ?", pantryID).
		Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count inventory items by pantry")
	}

	// Get paginated records
	offset := (page - 1) * limit
	if err := r.db.Where("pantry_id = ?", pantryID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory items by pantry")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, total, nil
}

// GetByPantryIDWithRelations retrieves inventory items by pantry ID with all related data and pagination
func (r *InventoryItemRepository) GetByPantryIDWithRelations(pantryID uuid.UUID, page, limit int) ([]*domain.InventoryItemWithRelations, int64, error) {
	var results []struct {
		InventoryItemModel
		PantryName      string  `gorm:"column:pantry_name"`
		LocationName    *string `gorm:"column:location_name"`
		ProductName     string  `gorm:"column:product_name"`
		ProductBrand    *string `gorm:"column:product_brand"`
		VariantName     string  `gorm:"column:variant_name"`
		VariantImageURL *string `gorm:"column:variant_image_url"`
		CategoryName    string  `gorm:"column:category_name"`
		UnitName        string  `gorm:"column:unit_name"`
		UnitSymbol      string  `gorm:"column:unit_symbol"`
	}
	var total int64

	baseQuery := r.db.Table("inventory_items i").
		Joins("JOIN pantries p ON i.pantry_id = p.pantry_id").
		Joins("LEFT JOIN pantry_locations pl ON i.location_id = pl.pantry_location_id").
		Joins("JOIN product_variants pv ON i.product_variant_id = pv.variant_id").
		Joins("JOIN products pr ON pv.product_id = pr.product_id").
		Joins("JOIN categories c ON pr.category_id = c.category_id").
		Joins("JOIN units_of_measure u ON i.unit_of_measure_id = u.unit_id").
		Where("i.pantry_id = ?", pantryID)

	// Count total records
	if err := baseQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count inventory items with relations")
	}

	// Get paginated records
	offset := (page - 1) * limit
	query := baseQuery.
		Select(`i.*,
			p.name as pantry_name,
			pl.name as location_name,
			pr.name as product_name,
			pr.brand as product_brand,
			pv.name as variant_name,
			pv.image_url as variant_image_url,
			c.name as category_name,
			u.name as unit_name,
			u.symbol as unit_symbol`).
		Order("i.created_at DESC").
		Offset(offset).
		Limit(limit)

	if err := query.Find(&results).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory items with relations")
	}

	items := make([]*domain.InventoryItemWithRelations, len(results))
	for i, result := range results {
		items[i] = &domain.InventoryItemWithRelations{
			InventoryItem:   result.InventoryItemModel.ToDomain(),
			PantryName:      result.PantryName,
			LocationName:    result.LocationName,
			ProductName:     result.ProductName,
			ProductBrand:    result.ProductBrand,
			VariantName:     result.VariantName,
			VariantImageURL: result.VariantImageURL,
			CategoryName:    result.CategoryName,
			UnitName:        result.UnitName,
			UnitSymbol:      result.UnitSymbol,
		}
	}

	return items, total, nil
}

// GetByLocationID retrieves inventory items by location ID
func (r *InventoryItemRepository) GetByLocationID(locationID uuid.UUID) ([]*domain.InventoryItem, error) {
	var models []InventoryItemModel

	if err := r.db.Where("location_id = ?", locationID).
		Order("created_at DESC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory items by location")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, nil
}

// GetByProductVariantID retrieves inventory items by product variant ID
func (r *InventoryItemRepository) GetByProductVariantID(productVariantID uuid.UUID) ([]*domain.InventoryItem, error) {
	var models []InventoryItemModel

	if err := r.db.Where("product_variant_id = ?", productVariantID).
		Order("created_at DESC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory items by product variant")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, nil
}

// GetExpiringItems retrieves items expiring within the specified number of days
func (r *InventoryItemRepository) GetExpiringItems(pantryID uuid.UUID, days int) ([]*domain.InventoryItem, error) {
	var models []InventoryItemModel

	thresholdDate := time.Now().AddDate(0, 0, days)

	if err := r.db.Where("pantry_id = ? AND expiration_date IS NOT NULL AND expiration_date <= ? AND quantity > 0",
		pantryID, thresholdDate).
		Order("expiration_date ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get expiring items")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, nil
}

// GetExpiringItemsWithRelations retrieves items that are expiring within the specified days with all related data
func (r *InventoryItemRepository) GetExpiringItemsWithRelations(pantryID uuid.UUID, days int) ([]*domain.InventoryItemWithRelations, error) {
	var results []struct {
		InventoryItemModel
		PantryName      string  `gorm:"column:pantry_name"`
		LocationName    *string `gorm:"column:location_name"`
		ProductName     string  `gorm:"column:product_name"`
		ProductBrand    *string `gorm:"column:product_brand"`
		VariantName     string  `gorm:"column:variant_name"`
		VariantImageURL *string `gorm:"column:variant_image_url"`
		CategoryName    string  `gorm:"column:category_name"`
		UnitName        string  `gorm:"column:unit_name"`
		UnitSymbol      string  `gorm:"column:unit_symbol"`
	}

	// Calculate the threshold date
	threshold := time.Now().AddDate(0, 0, days)

	query := r.db.Table("inventory_items i").
		Select(`i.*,
			p.name as pantry_name,
			pl.name as location_name,
			pr.name as product_name,
			pr.brand as product_brand,
			pv.name as variant_name,
			pv.image_url as variant_image_url,
			c.name as category_name,
			u.name as unit_name,
			u.symbol as unit_symbol`).
		Joins("JOIN pantries p ON i.pantry_id = p.pantry_id").
		Joins("LEFT JOIN pantry_locations pl ON i.location_id = pl.pantry_location_id").
		Joins("JOIN product_variants pv ON i.product_variant_id = pv.variant_id").
		Joins("JOIN products pr ON pv.product_id = pr.product_id").
		Joins("JOIN categories c ON pr.category_id = c.category_id").
		Joins("JOIN units_of_measure u ON i.unit_of_measure_id = u.unit_id").
		Where("i.pantry_id = ? AND i.expiration_date IS NOT NULL AND i.expiration_date <= ?", pantryID, threshold).
		Order("i.expiration_date ASC")

	if err := query.Find(&results).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get expiring items with relations")
	}

	items := make([]*domain.InventoryItemWithRelations, len(results))
	for i, result := range results {
		items[i] = &domain.InventoryItemWithRelations{
			InventoryItem:   result.InventoryItemModel.ToDomain(),
			PantryName:      result.PantryName,
			LocationName:    result.LocationName,
			ProductName:     result.ProductName,
			ProductBrand:    result.ProductBrand,
			VariantName:     result.VariantName,
			VariantImageURL: result.VariantImageURL,
			CategoryName:    result.CategoryName,
			UnitName:        result.UnitName,
			UnitSymbol:      result.UnitSymbol,
		}
	}

	return items, nil
}

// GetLowStockItems retrieves items with low stock (quantity < 1.0)
func (r *InventoryItemRepository) GetLowStockItems(pantryID uuid.UUID) ([]*domain.InventoryItem, error) {
	var models []InventoryItemModel

	if err := r.db.Where("pantry_id = ? AND quantity < ? AND quantity > 0", pantryID, 1.0).
		Order("quantity ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get low stock items")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, nil
}

// GetLowStockItemsWithRelations retrieves items with low stock for a pantry with all related data
func (r *InventoryItemRepository) GetLowStockItemsWithRelations(pantryID uuid.UUID) ([]*domain.InventoryItemWithRelations, error) {
	var results []struct {
		InventoryItemModel
		PantryName      string  `gorm:"column:pantry_name"`
		LocationName    *string `gorm:"column:location_name"`
		ProductName     string  `gorm:"column:product_name"`
		ProductBrand    *string `gorm:"column:product_brand"`
		VariantName     string  `gorm:"column:variant_name"`
		VariantImageURL *string `gorm:"column:variant_image_url"`
		CategoryName    string  `gorm:"column:category_name"`
		UnitName        string  `gorm:"column:unit_name"`
		UnitSymbol      string  `gorm:"column:unit_symbol"`
	}

	query := r.db.Table("inventory_items i").
		Select(`i.*,
			p.name as pantry_name,
			pl.name as location_name,
			pr.name as product_name,
			pr.brand as product_brand,
			pv.name as variant_name,
			pv.image_url as variant_image_url,
			c.name as category_name,
			u.name as unit_name,
			u.symbol as unit_symbol`).
		Joins("JOIN pantries p ON i.pantry_id = p.pantry_id").
		Joins("LEFT JOIN pantry_locations pl ON i.location_id = pl.pantry_location_id").
		Joins("JOIN product_variants pv ON i.product_variant_id = pv.variant_id").
		Joins("JOIN products pr ON pv.product_id = pr.product_id").
		Joins("JOIN categories c ON pr.category_id = c.category_id").
		Joins("JOIN units_of_measure u ON i.unit_of_measure_id = u.unit_id").
		Where("i.pantry_id = ? AND i.quantity < ? AND i.quantity > 0", pantryID, 1.0).
		Order("i.quantity ASC")

	if err := query.Find(&results).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get low stock items with relations")
	}

	items := make([]*domain.InventoryItemWithRelations, len(results))
	for i, result := range results {
		items[i] = &domain.InventoryItemWithRelations{
			InventoryItem:   result.InventoryItemModel.ToDomain(),
			PantryName:      result.PantryName,
			LocationName:    result.LocationName,
			ProductName:     result.ProductName,
			ProductBrand:    result.ProductBrand,
			VariantName:     result.VariantName,
			VariantImageURL: result.VariantImageURL,
			CategoryName:    result.CategoryName,
			UnitName:        result.UnitName,
			UnitSymbol:      result.UnitSymbol,
		}
	}

	return items, nil
}

// SearchItems searches for inventory items by query with pagination
func (r *InventoryItemRepository) SearchItems(pantryID uuid.UUID, query string, page, limit int) ([]*domain.InventoryItem, int64, error) {
	var models []InventoryItemModel
	var total int64

	// Build search query
	searchQuery := r.db.Model(&InventoryItemModel{}).
		Joins("LEFT JOIN product_variants ON inventory_items.product_variant_id = product_variants.variant_id").
		Joins("LEFT JOIN products ON product_variants.product_id = products.product_id").
		Where("inventory_items.pantry_id = ?", pantryID)

	if query != "" {
		searchTerm := "%" + strings.ToLower(query) + "%"
		searchQuery = searchQuery.Where(
			"LOWER(products.name) LIKE ? OR LOWER(product_variants.name) LIKE ? OR LOWER(inventory_items.notes) LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	// Count total records
	if err := searchQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count search results")
	}

	// Get paginated records
	offset := (page - 1) * limit
	if err := searchQuery.Select("inventory_items.*").
		Order("inventory_items.created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to search inventory items")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, total, nil
}

// SearchItemsWithRelations searches for inventory items by query with pagination and all related data
func (r *InventoryItemRepository) SearchItemsWithRelations(pantryID uuid.UUID, query string, page, limit int) ([]*domain.InventoryItemWithRelations, int64, error) {
	var results []struct {
		InventoryItemModel
		PantryName      string  `gorm:"column:pantry_name"`
		LocationName    *string `gorm:"column:location_name"`
		ProductName     string  `gorm:"column:product_name"`
		ProductBrand    *string `gorm:"column:product_brand"`
		VariantName     string  `gorm:"column:variant_name"`
		VariantImageURL *string `gorm:"column:variant_image_url"`
		CategoryName    string  `gorm:"column:category_name"`
		UnitName        string  `gorm:"column:unit_name"`
		UnitSymbol      string  `gorm:"column:unit_symbol"`
	}
	var total int64

	// Build base query with all joins
	baseQuery := r.db.Table("inventory_items i").
		Joins("JOIN pantries p ON i.pantry_id = p.pantry_id").
		Joins("LEFT JOIN pantry_locations pl ON i.location_id = pl.pantry_location_id").
		Joins("JOIN product_variants pv ON i.product_variant_id = pv.variant_id").
		Joins("JOIN products pr ON pv.product_id = pr.product_id").
		Joins("JOIN categories c ON pr.category_id = c.category_id").
		Joins("JOIN units_of_measure u ON i.unit_of_measure_id = u.unit_id").
		Where("i.pantry_id = ?", pantryID)

	// Add search conditions if query is provided
	if query != "" {
		searchTerm := "%" + strings.ToLower(query) + "%"
		baseQuery = baseQuery.Where(
			"LOWER(pr.name) LIKE ? OR LOWER(pv.name) LIKE ? OR LOWER(i.notes) LIKE ? OR LOWER(c.name) LIKE ?",
			searchTerm, searchTerm, searchTerm, searchTerm,
		)
	}

	// Count total records
	if err := baseQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count search results with relations")
	}

	// Get paginated records with all data
	offset := (page - 1) * limit
	searchQuery := baseQuery.
		Select(`i.*,
			p.name as pantry_name,
			pl.name as location_name,
			pr.name as product_name,
			pr.brand as product_brand,
			pv.name as variant_name,
			pv.image_url as variant_image_url,
			c.name as category_name,
			u.name as unit_name,
			u.symbol as unit_symbol`).
		Order("i.created_at DESC").
		Offset(offset).
		Limit(limit)

	if err := searchQuery.Find(&results).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to search inventory items with relations")
	}

	items := make([]*domain.InventoryItemWithRelations, len(results))
	for i, result := range results {
		items[i] = &domain.InventoryItemWithRelations{
			InventoryItem:   result.InventoryItemModel.ToDomain(),
			PantryName:      result.PantryName,
			LocationName:    result.LocationName,
			ProductName:     result.ProductName,
			ProductBrand:    result.ProductBrand,
			VariantName:     result.VariantName,
			VariantImageURL: result.VariantImageURL,
			CategoryName:    result.CategoryName,
			UnitName:        result.UnitName,
			UnitSymbol:      result.UnitSymbol,
		}
	}

	return items, total, nil
}

// FilterItemsWithRelations filters inventory items with comprehensive filtering options
func (r *InventoryItemRepository) FilterItemsWithRelations(pantryID uuid.UUID, filter *domain.InventoryFilterRequest) ([]*domain.InventoryItemWithRelations, int64, error) {
	var results []struct {
		InventoryItemModel
		PantryName      string  `gorm:"column:pantry_name"`
		LocationName    *string `gorm:"column:location_name"`
		ProductName     string  `gorm:"column:product_name"`
		ProductBrand    *string `gorm:"column:product_brand"`
		VariantName     string  `gorm:"column:variant_name"`
		VariantImageURL *string `gorm:"column:variant_image_url"`
		CategoryName    string  `gorm:"column:category_name"`
		UnitName        string  `gorm:"column:unit_name"`
		UnitSymbol      string  `gorm:"column:unit_symbol"`
	}
	var total int64

	// Build base query with all joins
	baseQuery := r.db.Table("inventory_items i").
		Joins("JOIN pantries p ON i.pantry_id = p.pantry_id").
		Joins("LEFT JOIN pantry_locations pl ON i.location_id = pl.pantry_location_id").
		Joins("JOIN product_variants pv ON i.product_variant_id = pv.variant_id").
		Joins("JOIN products pr ON pv.product_id = pr.product_id").
		Joins("JOIN categories c ON pr.category_id = c.category_id").
		Joins("JOIN units_of_measure u ON i.unit_of_measure_id = u.unit_id").
		Where("i.pantry_id = ?", pantryID)

	// Apply filters
	baseQuery = r.applyFilters(baseQuery, filter)

	// Count total records
	if err := baseQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count filtered inventory items")
	}

	// Apply pagination and sorting
	page := filter.Page
	if page < 1 {
		page = 1
	}
	limit := filter.Limit
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Apply sorting
	orderBy := r.buildOrderBy(filter.SortBy, filter.SortOrder)

	offset := (page - 1) * limit
	query := baseQuery.
		Select(`i.*,
			p.name as pantry_name,
			pl.name as location_name,
			pr.name as product_name,
			pr.brand as product_brand,
			pv.name as variant_name,
			pv.image_url as variant_image_url,
			c.name as category_name,
			u.name as unit_name,
			u.symbol as unit_symbol`).
		Order(orderBy).
		Offset(offset).
		Limit(limit)

	if err := query.Find(&results).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to filter inventory items with relations")
	}

	// Convert to domain objects
	items := make([]*domain.InventoryItemWithRelations, len(results))
	for i, result := range results {
		items[i] = &domain.InventoryItemWithRelations{
			InventoryItem:   result.InventoryItemModel.ToDomain(),
			PantryName:      result.PantryName,
			LocationName:    result.LocationName,
			ProductName:     result.ProductName,
			ProductBrand:    result.ProductBrand,
			VariantName:     result.VariantName,
			VariantImageURL: result.VariantImageURL,
			CategoryName:    result.CategoryName,
			UnitName:        result.UnitName,
			UnitSymbol:      result.UnitSymbol,
		}
	}

	return items, total, nil
}

// Update updates an existing inventory item
func (r *InventoryItemRepository) Update(item *domain.InventoryItem) error {
	model := &InventoryItemModel{}
	model.FromDomain(item)

	result := r.db.Model(&InventoryItemModel{}).
		Where("item_id = ?", item.ID).
		Updates(model)

	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update inventory item")
	}

	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "inventory item not found")
	}

	return nil
}

// Delete soft deletes an inventory item
func (r *InventoryItemRepository) Delete(id uuid.UUID) error {
	result := r.db.Where("item_id = ?", id).Delete(&InventoryItemModel{})

	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete inventory item")
	}

	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "inventory item not found")
	}

	return nil
}

// GetTotalQuantityByProductVariant gets the total quantity of a product variant in a pantry
func (r *InventoryItemRepository) GetTotalQuantityByProductVariant(pantryID uuid.UUID, productVariantID uuid.UUID) (float64, error) {
	var totalQuantity float64

	if err := r.db.Model(&InventoryItemModel{}).
		Where("pantry_id = ? AND product_variant_id = ? AND quantity > 0", pantryID, productVariantID).
		Select("COALESCE(SUM(quantity), 0)").
		Scan(&totalQuantity).Error; err != nil {
		return 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get total quantity by product variant")
	}

	return totalQuantity, nil
}

// applyFilters applies filtering conditions to the query
func (r *InventoryItemRepository) applyFilters(query *gorm.DB, filter *domain.InventoryFilterRequest) *gorm.DB {
	if filter == nil {
		return query
	}

	// Text search
	if filter.Query != "" {
		searchTerm := "%" + strings.ToLower(filter.Query) + "%"
		query = query.Where(
			"LOWER(pr.name) LIKE ? OR LOWER(pv.name) LIKE ? OR LOWER(pr.brand) LIKE ? OR LOWER(i.notes) LIKE ?",
			searchTerm, searchTerm, searchTerm, searchTerm,
		)
	}

	// Location filtering
	if filter.LocationID != nil {
		query = query.Where("i.location_id = ?", *filter.LocationID)
	}
	if len(filter.LocationIDs) > 0 {
		query = query.Where("i.location_id IN ?", filter.LocationIDs)
	}

	// Category filtering
	if filter.CategoryID != nil {
		if filter.IncludeSubcategories {
			// Include subcategories - need to join with categories table for hierarchy
			query = query.Where("(c.category_id = ? OR c.parent_category_id = ?)", *filter.CategoryID, *filter.CategoryID)
		} else {
			query = query.Where("c.category_id = ?", *filter.CategoryID)
		}
	}
	if len(filter.CategoryIDs) > 0 {
		if filter.IncludeSubcategories {
			query = query.Where("(c.category_id IN ? OR c.parent_category_id IN ?)", filter.CategoryIDs, filter.CategoryIDs)
		} else {
			query = query.Where("c.category_id IN ?", filter.CategoryIDs)
		}
	}

	// Product filtering
	if filter.ProductID != nil {
		query = query.Where("pr.product_id = ?", *filter.ProductID)
	}
	if filter.ProductVariantID != nil {
		query = query.Where("i.product_variant_id = ?", *filter.ProductVariantID)
	}
	if filter.Brand != nil {
		query = query.Where("LOWER(pr.brand) = LOWER(?)", *filter.Brand)
	}

	// Status filtering
	if filter.Status != nil {
		// This would need to be implemented based on business logic for status calculation
		// For now, we'll skip this as it requires complex logic
	}

	// Stock status filtering
	if filter.StockStatus != nil {
		switch *filter.StockStatus {
		case domain.StockStatusOut:
			query = query.Where("i.quantity = 0")
		case domain.StockStatusLow:
			query = query.Where("i.quantity > 0 AND i.quantity < 5") // Configurable threshold
		case domain.StockStatusWellStocked:
			query = query.Where("i.quantity >= 5") // Configurable threshold
		}
	}

	// Expiration filtering
	if filter.ExpirationStatus != nil {
		now := time.Now()
		switch *filter.ExpirationStatus {
		case domain.ExpirationStatusExpired:
			query = query.Where("i.expiration_date IS NOT NULL AND i.expiration_date < ?", now)
		case domain.ExpirationStatusWarning:
			// Expiring in 7 days (configurable)
			warningDate := now.AddDate(0, 0, 7)
			query = query.Where("i.expiration_date IS NOT NULL AND i.expiration_date BETWEEN ? AND ?", now, warningDate)
		case domain.ExpirationStatusAlert:
			// Expiring in 3 days (configurable)
			alertDate := now.AddDate(0, 0, 3)
			query = query.Where("i.expiration_date IS NOT NULL AND i.expiration_date BETWEEN ? AND ?", now, alertDate)
		case domain.ExpirationStatusCritical:
			// Expiring in 1 day (configurable)
			criticalDate := now.AddDate(0, 0, 1)
			query = query.Where("i.expiration_date IS NOT NULL AND i.expiration_date BETWEEN ? AND ?", now, criticalDate)
		}
	}

	if filter.ExpiringInDays != nil {
		thresholdDate := time.Now().AddDate(0, 0, *filter.ExpiringInDays)
		query = query.Where("i.expiration_date IS NOT NULL AND i.expiration_date <= ?", thresholdDate)
	}

	if filter.ExpiredOnly != nil && *filter.ExpiredOnly {
		query = query.Where("i.expiration_date IS NOT NULL AND i.expiration_date < ?", time.Now())
	}

	// Date range filtering
	if filter.PurchaseDateFrom != nil {
		query = query.Where("i.purchase_date >= ?", *filter.PurchaseDateFrom)
	}
	if filter.PurchaseDateTo != nil {
		query = query.Where("i.purchase_date <= ?", *filter.PurchaseDateTo)
	}
	if filter.CreatedFrom != nil {
		query = query.Where("i.created_at >= ?", *filter.CreatedFrom)
	}
	if filter.CreatedTo != nil {
		query = query.Where("i.created_at <= ?", *filter.CreatedTo)
	}

	// Quantity filtering
	if filter.MinQuantity != nil {
		query = query.Where("i.quantity >= ?", *filter.MinQuantity)
	}
	if filter.MaxQuantity != nil {
		query = query.Where("i.quantity <= ?", *filter.MaxQuantity)
	}

	return query
}

// buildOrderBy builds the ORDER BY clause based on sort parameters
func (r *InventoryItemRepository) buildOrderBy(sortBy, sortOrder string) string {
	// Default sorting
	if sortBy == "" {
		sortBy = "created_at"
	}
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Validate sort order
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc"
	}

	// Map sort fields to actual column names
	var column string
	switch sortBy {
	case "created_at":
		column = "i.created_at"
	case "updated_at":
		column = "i.updated_at"
	case "quantity":
		column = "i.quantity"
	case "expiration_date":
		column = "i.expiration_date"
	case "purchase_date":
		column = "i.purchase_date"
	case "product_name":
		column = "pr.name"
	case "variant_name":
		column = "pv.name"
	case "category_name":
		column = "c.name"
	case "location_name":
		column = "pl.name"
	case "brand":
		column = "pr.brand"
	default:
		column = "i.created_at"
	}

	return column + " " + strings.ToUpper(sortOrder)
}
