# Pantry Pal Backend Makefile

# Variables
APP_NAME=pantry-pal
BINARY_NAME=pantry-pal-api
DOCKER_IMAGE=pantry-pal:latest
MIGRATION_PATH=internal/infra/persistence/migrations
DATABASE_URL=postgres://pantrypal:pantrypal_dev_password@localhost:5432/pantrypal?sslmode=disable

# Go related variables
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build the application
.PHONY: build
build:
	$(GOBUILD) -o bin/$(BINARY_NAME) cmd/api/main.go

# Run the application
.PHONY: run
run:
	@if [ -f .env ]; then \
		export $$(cat .env | grep -v '^#' | xargs) && $(GOCMD) run cmd/api/main.go; \
	else \
		echo "Warning: .env file not found. Make sure to set required environment variables."; \
		$(GOCMD) run cmd/api/main.go; \
	fi

# Run with live reload (requires air: go install github.com/cosmtrek/air@latest)
.PHONY: dev
dev:
	air

# Clean build artifacts
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -f bin/$(BINARY_NAME)

# Run tests
.PHONY: test
test:
	$(GOTEST) -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# Run unit tests only (exclude integration tests)
.PHONY: test-unit
test-unit:
	$(GOTEST) -v ./... -short

# Run integration tests only
.PHONY: test-integration
test-integration:
	$(GOTEST) -v ./internal/test/integration/... -timeout=10m

# Run all endpoint tests
.PHONY: test-endpoints
test-endpoints:
	$(GOTEST) -v ./internal/test/integration/... -run TestAllEndpointsTestSuite -timeout=15m

# Run specific handler tests
.PHONY: test-auth
test-auth:
	$(GOTEST) -v ./internal/test/integration/... -run TestAuthTestSuite -timeout=5m

.PHONY: test-category
test-category:
	$(GOTEST) -v ./internal/test/integration/... -run TestCategoryTestSuite -timeout=5m

.PHONY: test-pantry
test-pantry:
	$(GOTEST) -v ./internal/test/integration/... -run TestPantryTestSuite -timeout=5m

# Download dependencies
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Format code
.PHONY: fmt
fmt:
	$(GOCMD) fmt ./...

# Lint code (requires golangci-lint)
.PHONY: lint
lint:
	golangci-lint run

# Start development environment (PostgreSQL + Redis)
.PHONY: docker-dev-up
docker-dev-up:
	docker-compose up -d postgres redis

# Stop development environment
.PHONY: docker-dev-down
docker-dev-down:
	docker-compose down

# Start all services including the app
.PHONY: docker-up
docker-up:
	docker-compose up -d

# Stop all services
.PHONY: docker-down
docker-down:
	docker-compose down

# View logs
.PHONY: docker-logs
docker-logs:
	docker-compose logs -f

# Database migrations and seeds
.PHONY: migrate-up
migrate-up:
	migrate -path $(MIGRATION_PATH) -database "$(DATABASE_URL)" up

.PHONY: migrate-down
migrate-down:
	migrate -path $(MIGRATION_PATH) -database "$(DATABASE_URL)" down

.PHONY: migrate-force
migrate-force:
	migrate -path $(MIGRATION_PATH) -database "$(DATABASE_URL)" force $(VERSION)

.PHONY: migrate-version
migrate-version:
	migrate -path $(MIGRATION_PATH) -database "$(DATABASE_URL)" version

# Create a new migration file
.PHONY: migrate-create
migrate-create:
	@read -p "Enter migration name: " name; \
	migrate create -ext sql -dir $(MIGRATION_PATH) -seq $$name

# Database operations
.PHONY: db-reset
db-reset: migrate-down migrate-up

# Seed database with test data
.PHONY: seed-test-user
seed-test-user:
	PGPASSWORD=pantrypal_dev_password psql -h localhost -U pantrypal -d pantrypal -f scripts/seed-test-user.sql

.PHONY: seed-test-data
seed-test-data:
	PGPASSWORD=pantrypal_dev_password psql -h localhost -U pantrypal -d pantrypal -f scripts/seed-test-data.sql

.PHONY: seed-additional-data
seed-additional-data:
	PGPASSWORD=pantrypal_dev_password psql -h localhost -U pantrypal -d pantrypal -f scripts/seed-additional-data.sql

.PHONY: seed-all
seed-all: seed-test-user seed-test-data seed-additional-data

# Comprehensive seed data for testing and simulation
.PHONY: seed-comprehensive
seed-comprehensive:
	@echo "Seeding comprehensive test data..."
	PGPASSWORD=pantrypal_dev_password psql -h localhost -U pantrypal -d pantrypal -f scripts/seed-comprehensive-data.sql
	@echo "Comprehensive data seeded successfully!"

.PHONY: seed-inventory-recipes
seed-inventory-recipes:
	@echo "Seeding inventory and recipe data..."
	PGPASSWORD=pantrypal_dev_password psql -h localhost -U pantrypal -d pantrypal -f scripts/seed-inventory-recipes.sql
	@echo "Inventory and recipe data seeded successfully!"

.PHONY: seed-shopping-alerts
seed-shopping-alerts:
	@echo "Seeding shopping lists and alert configurations..."
	PGPASSWORD=pantrypal_dev_password psql -h localhost -U pantrypal -d pantrypal -f scripts/seed-shopping-alerts.sql
	@echo "Shopping and alert data seeded successfully!"

# Corrected comprehensive seed data (single file with correct schemas)
.PHONY: seed-corrected
seed-corrected:
	@echo "🌱 Seeding corrected comprehensive test data..."
	PGPASSWORD=pantrypal_dev_password psql -h localhost -U pantrypal -d pantrypal -f scripts/seed-corrected-data.sql
	@echo "✅ Corrected comprehensive data seeded successfully!"

# Indonesian comprehensive seed data
.PHONY: seed-comprehensive-indonesia
seed-comprehensive-indonesia:
	@echo "🌱 Seeding Indonesian comprehensive test data..."
	PGPASSWORD=pantrypal_dev_password psql -h localhost -U pantrypal -d pantrypal -f scripts/seed-comprehensive-data-indonesia.sql
	@echo "✅ Indonesian comprehensive data seeded successfully!"

.PHONY: seed-inventory-recipes-indonesia
seed-inventory-recipes-indonesia:
	@echo "🌱 Seeding Indonesian inventory and recipe data..."
	PGPASSWORD=pantrypal_dev_password psql -h localhost -U pantrypal -d pantrypal -f scripts/seed-inventory-recipes-indonesia.sql
	@echo "✅ Indonesian inventory and recipe data seeded successfully!"

.PHONY: seed-complete-indonesia
seed-complete-indonesia: seed-comprehensive-indonesia seed-inventory-recipes-indonesia
	@echo "🎉 Complete Indonesian seed data loaded successfully!"
	@echo ""
	@echo "📊 Indonesian Seeded Data Summary:"
	@echo "  👥 Users: 8 Indonesian test users (Budi Santoso, Sari Dewi, etc.)"
	@echo "  🏪 Stores: 5 Indonesian stores (Indomaret, Alfamart, Hypermart, etc.)"
	@echo "  🏠 Pantries: 6 Indonesian pantries (Dapur Budi, Apartemen Sari, etc.)"
	@echo "  👥 Memberships: 11 memberships with different roles"
	@echo "  📍 Locations: 20 Indonesian storage locations (Lemari Dapur, Kulkas, etc.)"
	@echo "  🛒 Products: 20 Indonesian products (Susu Segar, Telur Ayam, etc.)"
	@echo "  📦 Product Variants: 18 Indonesian variants with local brands"
	@echo "  📦 Inventory: 22 inventory items with Rupiah pricing"
	@echo "  🏷️ Recipe Tags: 10 Indonesian recipe tags (Cepat & Mudah, Sehat, etc.)"
	@echo "  🍳 Recipes: 5 Indonesian recipes (Telur Orak-Arik, Tumis Ayam Sayur, etc.)"
	@echo ""
	@echo "🧪 Ready for comprehensive Indonesian endpoint testing and simulation!"
	@echo "📖 See scripts/README.md for detailed documentation"

.PHONY: seed-complete
seed-complete: seed-corrected
	@echo "🎉 Complete seed data loaded successfully!"
	@echo ""
	@echo "📊 Seeded Data Summary:"
	@echo "  👥 Users: 6 test users with different roles and scenarios"
	@echo "  🏪 Stores: 3 stores for purchase history testing"
	@echo "  🏠 Pantries: 3 pantries (personal, apartment, restaurant)"
	@echo "  👥 Memberships: 4 memberships with different roles"
	@echo "  📍 Locations: 7 storage locations across pantries"
	@echo "  🛒 Products: 6 products across major categories"
	@echo "  📦 Product Variants: 6 variants with barcodes and units"
	@echo "  📦 Inventory: 9 inventory items with expiration dates"
	@echo "  🏷️ Recipe Tags: 5 categorization tags"
	@echo "  🍳 Recipes: 3 recipes with different complexity levels"
	@echo "  🛍️ Shopping Lists: 3 shopping lists with 5 items"
	@echo "  🔔 Alerts: 3 alert configurations for different scenarios"
	@echo "  📬 Notifications: 2 sample notifications (sent and pending)"
	@echo ""
	@echo "🧪 Ready for comprehensive endpoint testing and simulation!"
	@echo "📖 See scripts/README.md for detailed documentation"

.PHONY: seed-reset
seed-reset:
	@echo "⚠️  Resetting all seed data..."
	@echo "This will clear all existing data and reload fresh seed data."
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	@make db-reset
	@make seed-complete
	@echo "✅ Database reset and reseeded successfully!"

# Install development tools
.PHONY: install-tools
install-tools:
	go install github.com/cosmtrek/air@latest
	go install github.com/golang-migrate/migrate/v4/cmd/migrate@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Setup development environment
.PHONY: setup
setup: install-tools deps docker-dev-up
	@echo "Waiting for database to be ready..."
	@sleep 5
	@make migrate-up
	@echo "Development environment is ready!"

# Health check
.PHONY: health
health:
	curl -f http://localhost:8080/health || exit 1

# Build Docker image
.PHONY: docker-build
docker-build:
	docker build -t $(DOCKER_IMAGE) .

# Help
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  build          - Build the application binary"
	@echo "  run            - Run the application"
	@echo "  dev            - Run with live reload (requires air)"
	@echo "  clean          - Clean build artifacts"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage report"
	@echo "  deps           - Download and tidy dependencies"
	@echo "  fmt            - Format code"
	@echo "  lint           - Lint code"
	@echo "  docker-dev-up  - Start development services (DB + Redis)"
	@echo "  docker-dev-down- Stop development services"
	@echo "  docker-up      - Start all services"
	@echo "  docker-down    - Stop all services"
	@echo "  docker-logs    - View service logs"
	@echo "  migrate-up     - Run database migrations"
	@echo "  migrate-down   - Rollback database migrations"
	@echo "  migrate-create - Create new migration file"
	@echo "  db-reset       - Reset database (down + up)"
	@echo "  seed-test-user - Add test user to database"
	@echo "  seed-test-data - Add initial test data"
	@echo "  seed-additional-data - Add more test data"
	@echo "  seed-all       - Add all test data (user + initial + additional)"
	@echo "  seed-comprehensive - Seed comprehensive test data (users, pantries, products)"
	@echo "  seed-inventory-recipes - Seed inventory items and recipes"
	@echo "  seed-shopping-alerts - Seed shopping lists and alert configurations"
	@echo "  seed-corrected - Seed corrected comprehensive data (single file, correct schemas)"
	@echo "  seed-complete  - Seed all comprehensive data for testing and simulation"
	@echo "  seed-comprehensive-indonesia - Seed Indonesian comprehensive test data"
	@echo "  seed-inventory-recipes-indonesia - Seed Indonesian inventory and recipe data"
	@echo "  seed-complete-indonesia - Seed all Indonesian comprehensive data"
	@echo "  seed-reset     - Reset database and reload all seed data"
	@echo "  setup          - Setup complete development environment"
	@echo "  health         - Check application health"
	@echo "  help           - Show this help message"
