{"id": "pantry-pal-environment", "name": "Pantry Pal Environment", "values": [{"key": "base_url", "value": "localhost:8080", "description": "Base URL for the Pantry Pal API", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "description": "JWT access token (auto-populated after login)", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "description": "JWT refresh token (auto-populated after login)", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "description": "Current user ID (auto-populated after login)", "type": "default", "enabled": true}, {"key": "pantry_id", "value": "", "description": "Current pantry ID (set manually or auto-populated)", "type": "default", "enabled": true}, {"key": "item_id", "value": "", "description": "Current inventory item ID (set manually or auto-populated)", "type": "default", "enabled": true}, {"key": "recipe_id", "value": "", "description": "Current recipe ID (set manually or auto-populated)", "type": "default", "enabled": true}, {"key": "category_id", "value": "", "description": "Current category ID (set manually or auto-populated)", "type": "default", "enabled": true}, {"key": "unit_id", "value": "", "description": "Current unit of measure ID (set manually or auto-populated)", "type": "default", "enabled": true}, {"key": "product_id", "value": "", "description": "Current product ID (set manually or auto-populated)", "type": "default", "enabled": true}, {"key": "variant_id", "value": "", "description": "Current product variant ID (set manually or auto-populated)", "type": "default", "enabled": true}, {"key": "location_id", "value": "", "description": "Current pantry location ID (set manually or auto-populated)", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-15T10:30:00.000Z", "_postman_exported_using": "Postman/10.0.0"}