# Pantry Pal API - Postman Collection

This directory contains a comprehensive Postman collection for the Pantry Pal API, including all endpoints with authentication, documentation, and example requests.

## 📁 Files

* `Pantry-Pal-API.postman_collection.json` - Main collection with all API endpoints
* `Pantry-Pal-Environment.postman_environment.json` - Environment variables for the collection
* `README.md` - This documentation file

## 🚀 Quick Start

### 1. Import Collection and Environment

1. Open Postman
2. Click **Import** button
3. Select both JSON files:
   - `Pantry-Pal-API.postman_collection.json`

   - `Pantry-Pal-Environment.postman_environment.json`

4. Click **Import**

### 2. Set Environment

1. In Postman, select the **Pantry Pal Environment** from the environment dropdown (top right)
2. Verify the `base_url` is set correctly (default: `localhost:8080`)

### 3. Start Using the API

1. **Register a new user** or **Login** with existing credentials
2. The access token will be automatically captured and stored in environment variables
3. All protected endpoints will automatically use the Bearer token authentication

## 🔐 Authentication

The API uses JWT Bearer token authentication:

* **Public endpoints**: `/auth/register`,  `/auth/login`,  `/auth/refresh`,  `/auth/logout`
* **Protected endpoints**: All other endpoints require `Authorization: Bearer <token>` header

### Authentication Flow

1. **Register** (`POST /auth/register`) or **Login** (`POST /auth/login`)
2. Access token is automatically saved to `access_token` environment variable
3. All subsequent requests use this token automatically
4. Use **Refresh Token** (`POST /auth/refresh`) when access token expires
5. **Logout** (`POST /auth/logout`) to revoke tokens

## 📚 API Endpoints Overview

### Authentication

* `POST /auth/register` - Register new user
* `POST /auth/login` - User login
* `POST /auth/refresh` - Refresh access token
* `POST /auth/logout` - User logout

### User Management

* `GET /users/profile` - Get user profile
* `PUT /users/profile` - Update user profile
* `POST /users/change-password` - Change password

### Pantry Management

* `POST /pantries` - Create pantry
* `GET /pantries` - Get all pantries
* `GET /pantries/{id}` - Get pantry by ID
* `PUT /pantries/{id}` - Update pantry
* `DELETE /pantries/{id}` - Delete pantry

### Inventory Management

* `POST /pantries/{id}/inventory` - Add inventory item
* `GET /pantries/{id}/inventory` - Get pantry inventory
* `GET /inventory/{id}` - Get inventory item
* `PUT /inventory/{id}` - Update inventory item
* `POST /inventory/{id}/consume` - Consume inventory item

### Product Catalog

* **Categories**: CRUD operations for product categories
* **Units**: Management of units of measure
* **Products**: Product management with variants
* **Variants**: Product variant management

### Recipe Management

* `POST /recipes` - Create recipe
* `GET /recipes` - Get all recipes
* `GET /recipes/{id}` - Get recipe by ID
* `PUT /recipes/{id}` - Update recipe
* `DELETE /recipes/{id}` - Delete recipe
* `POST /recipes/{id}/check-ingredients` - Check ingredient availability

### Pantry Locations

* `POST /pantries/{id}/locations` - Create pantry location
* `GET /pantries/{id}/locations` - Get pantry locations
* `PUT /pantries/{id}/locations/{id}` - Update pantry location
* `DELETE /pantries/{id}/locations/{id}` - Delete pantry location

### Shopping Lists

* `GET /pantries/{id}/shopping-list` - Get shopping list
* `POST /pantries/{id}/shopping-list/items` - Add shopping list item
* `POST /pantries/{id}/shopping-list/generate-from-recipe` - Generate from recipe
* `POST /pantries/{id}/shopping-list/items/{id}/purchase` - Mark item as purchased

### Expiration Tracking

* `POST /expiration/alerts/global` - Configure global alerts
* `GET /expiration/alerts/global` - Get global alert configuration
* `POST /pantries/{id}/expiration/alerts` - Configure pantry alerts
* `GET /pantries/{id}/expiration/items` - Get expiring items

### Health Check

* `GET /health` - API health status

## 🔧 Environment Variables

The environment includes these variables that are automatically managed:

| Variable | Description | Auto-populated |
|----------|-------------|----------------|
| `base_url` | API base URL | Manual |
| `access_token` | JWT access token | ✅ After login |
| `refresh_token` | JWT refresh token | ✅ After login |
| `user_id` | Current user ID | ✅ After login |
| `pantry_id` | Current pantry ID | ✅ After pantry creation |
| `item_id` | Current inventory item ID | ✅ After item creation |
| `recipe_id` | Current recipe ID | Manual/Auto |
| `category_id` | Current category ID | Manual/Auto |
| `unit_id` | Current unit ID | Manual/Auto |
| `product_id` | Current product ID | Manual/Auto |
| `variant_id` | Current variant ID | Manual/Auto |
| `location_id` | Current location ID | Manual/Auto |

## 📝 Usage Tips

### 1. Test Scripts

Each request includes test scripts that:
* Validate response status codes
* Check response structure
* Auto-save important IDs to environment variables

### 2. Request Examples

All requests include realistic example data in request bodies.

### 3. Response Examples

Key endpoints include example responses showing expected data structure.

### 4. Filtering and Pagination

Many GET endpoints support query parameters for:
* Pagination (`page`,  `limit`)
* Filtering (`category_id`,  `location_id`, etc.)
* Searching (`search` parameter)
* Sorting (`sort_by`,  `sort_order`)

### 5. Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"]
}
```

## 🔄 Workflow Examples

### Basic Workflow

1. Register/Login → Get access token
2. Create pantry → Get pantry ID
3. Add inventory items → Track items
4. Create recipes → Check ingredient availability

### Recipe Workflow

1. Create categories and units
2. Create products and variants
3. Add inventory items to pantry
4. Create recipe with ingredients
5. Check if pantry has sufficient ingredients

## 🛠️ Development Setup

If running the API locally:

1. Start the Pantry Pal API server
2. Ensure it's running on `localhost:8080` (or update `base_url`)
3. Import the collection and environment
4. Start testing!

## 📞 Support

For API documentation, visit: `http://localhost:8080/docs/` (Swagger UI)

For issues or questions about the API collection, refer to the main project documentation.
