{"info": {"_postman_id": "pantry-pal-api-collection", "name": "Pantry Pal API", "description": "A comprehensive multi-tenant pantry management system for tracking inventory, managing shopping lists, and reducing food waste.\n\n## Authentication\nThis API uses JWT Bearer token authentication. After logging in, the access token will be automatically set in the environment variable `access_token` and used for all protected endpoints.\n\n## Base URL\n`{{base_url}}/api/v1`\n\n## Getting Started\n1. Import this collection and the environment file\n2. Set your base URL in the environment (default: localhost:8080)\n3. Register a new user or login with existing credentials\n4. The access token will be automatically captured and used for subsequent requests\n\n## Environment Variables\n- `base_url`: API base URL (default: localhost:8080)\n- `access_token`: JWT access token (auto-populated after login)\n- `refresh_token`: JWT refresh token (auto-populated after login)\n- `user_id`: Current user ID (auto-populated after login)\n- `pantry_id`: Current pantry ID (set manually or auto-populated)\n- `item_id`: Current inventory item ID (set manually or auto-populated)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "pantry-pal-api"}, "item": [{"name": "Authentication", "description": "User authentication endpoints including registration, login, logout, and token refresh.", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        console.log('Access token saved to environment');", "    }", "    if (response.data && response.data.user && response.data.user.id) {", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('User ID saved to environment');", "    }", "}", "", "pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has access token', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('access_token');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePassword123!\",\n    \"first_name\": \"Test\",\n    \"last_name\": \"User\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Create a new user account with email, username, and password. Returns access and refresh tokens upon successful registration."}, "response": [{"name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePassword123!\",\n    \"first_name\": \"Test\",\n    \"last_name\": \"User\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"User registered successfully\",\n    \"data\": {\n        \"access_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n        \"token_type\": \"Bearer\",\n        \"expires_in\": 3600,\n        \"user\": {\n            \"id\": \"123e4567-e89b-12d3-a456-426614174000\",\n            \"username\": \"testuser\",\n            \"email\": \"<EMAIL>\",\n            \"first_name\": \"Test\",\n            \"last_name\": \"User\",\n            \"created_at\": \"2024-01-15T10:30:00Z\",\n            \"updated_at\": \"2024-01-15T10:30:00Z\"\n        }\n    }\n}"}]}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        console.log('Access token saved to environment');", "    }", "    if (response.data && response.data.user && response.data.user.id) {", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('User ID saved to environment');", "    }", "}", "", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has access token', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('access_token');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Authenticate user with email and password. Returns access token and user information."}, "response": []}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh"]}, "description": "Generate new access token using refresh token from cookie. The refresh token is automatically sent via HTTP-only cookie."}, "response": []}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}, "description": "Logout user and revoke refresh token. Clears authentication cookies."}, "response": []}]}, {"name": "User Management", "description": "User profile management endpoints including profile retrieval, updates, and password changes.", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Retrieve the current user's profile information including username, email, and personal details."}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"Updated First\",\n    \"last_name\": \"Updated Last\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Update the current user's profile information. Only first_name and last_name can be updated."}, "response": []}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"CurrentPassword123!\",\n    \"new_password\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/change-password", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "change-password"]}, "description": "Change the current user's password. Requires current password for verification."}, "response": []}]}, {"name": "Pantry Management", "description": "Pantry creation, management, and membership endpoints including invitations and ownership transfer.", "item": [{"name": "Create Pantry", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('pantry_id', response.data.id);", "        console.log('Pantry ID saved to environment: ' + response.data.id);", "    }", "}", "", "pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"My Kitchen Pantry\",\n    \"description\": \"Main kitchen pantry for household items\",\n    \"type\": \"kitchen\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/pantries", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries"]}, "description": "Create a new pantry. The user becomes the owner automatically."}, "response": []}, {"name": "Get All Pantries", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/pantries", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries"]}, "description": "Retrieve all pantries the current user has access to (owned or member)."}, "response": []}, {"name": "Get Pantry by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}"]}, "description": "Retrieve detailed information about a specific pantry including locations and member count."}, "response": []}, {"name": "Update Pantry", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Kitchen Pantry\",\n    \"description\": \"Updated description for main kitchen pantry\",\n    \"type\": \"kitchen\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}"]}, "description": "Update pantry information. Only owners can update pantry details."}, "response": []}, {"name": "Delete Pantry", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}"]}, "description": "Delete a pantry. Only owners can delete pantries. This will also delete all associated data."}, "response": []}]}, {"name": "Inventory Management", "description": "Inventory item tracking, management, and bulk operations within pantries.", "item": [{"name": "Add Inventory Item", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('item_id', response.data.id);", "        console.log('Item ID saved to environment: ' + response.data.id);", "    }", "}", "", "pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"pantry_id\": \"{{pantry_id}}\",\n    \"product_variant_id\": \"product-variant-uuid\",\n    \"location_id\": \"location-uuid\",\n    \"quantity\": 2.5,\n    \"unit_id\": \"unit-uuid\",\n    \"purchase_date\": \"2024-01-15\",\n    \"expiration_date\": \"2024-02-15\",\n    \"cost\": 4.99,\n    \"notes\": \"Organic brand from local store\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/inventory", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "inventory"]}, "description": "Add a new inventory item to a pantry. Returns enhanced response with human-readable names."}, "response": []}, {"name": "Get Pantry Inventory", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/inventory?page=1&limit=20&location_id=&category_id=&search=&sort_by=expiration_date&sort_order=asc", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "inventory"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of items per page"}, {"key": "location_id", "value": "", "description": "Filter by location ID"}, {"key": "category_id", "value": "", "description": "Filter by category ID"}, {"key": "search", "value": "", "description": "Search term for product names"}, {"key": "sort_by", "value": "expiration_date", "description": "Sort field (expiration_date, purchase_date, quantity, etc.)"}, {"key": "sort_order", "value": "asc", "description": "Sort order (asc or desc)"}]}, "description": "Retrieve all inventory items in a pantry with filtering, searching, and pagination options."}, "response": []}, {"name": "Filter Pantry Inventory", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/inventory/filter?query=&location_id=&location_ids=&category_id=&category_ids=&include_subcategories=false&product_id=&product_variant_id=&brand=&stock_status=&expiration_status=&expiring_in_days=&expired_only=false&min_quantity=&max_quantity=&purchase_date_from=&purchase_date_to=&created_from=&created_to=&sort_by=created_at&sort_order=desc&page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "inventory", "filter"], "query": [{"key": "query", "value": "", "description": "Text search across product names, variants, brands, and notes"}, {"key": "location_id", "value": "", "description": "Filter by specific location ID"}, {"key": "location_ids", "value": "", "description": "Filter by multiple location IDs (comma-separated)"}, {"key": "category_id", "value": "", "description": "Filter by specific category ID"}, {"key": "category_ids", "value": "", "description": "Filter by multiple category IDs (comma-separated)"}, {"key": "include_subcategories", "value": "false", "description": "Include subcategories when filtering by category"}, {"key": "product_id", "value": "", "description": "Filter by specific product ID"}, {"key": "product_variant_id", "value": "", "description": "Filter by specific product variant ID"}, {"key": "brand", "value": "", "description": "Filter by product brand"}, {"key": "stock_status", "value": "", "description": "Filter by stock status (low, out, well_stocked, all)"}, {"key": "expiration_status", "value": "", "description": "Filter by expiration status (expired, warning, alert, critical)"}, {"key": "expiring_in_days", "value": "", "description": "Filter items expiring within specified days"}, {"key": "expired_only", "value": "false", "description": "Show only expired items"}, {"key": "min_quantity", "value": "", "description": "Minimum quantity filter"}, {"key": "max_quantity", "value": "", "description": "Maximum quantity filter"}, {"key": "purchase_date_from", "value": "", "description": "Filter by purchase date from (YYYY-MM-DD)"}, {"key": "purchase_date_to", "value": "", "description": "Filter by purchase date to (YYYY-MM-DD)"}, {"key": "created_from", "value": "", "description": "Filter by creation date from (YYYY-MM-DD)"}, {"key": "created_to", "value": "", "description": "Filter by creation date to (YYYY-MM-DD)"}, {"key": "sort_by", "value": "created_at", "description": "Sort field (created_at, updated_at, quantity, expiration_date, purchase_date, product_name, variant_name, category_name, location_name, brand)"}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)"}, {"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Items per page (max: 100)"}]}, "description": "Advanced filtering for inventory items with comprehensive search and filter options including location, category, stock status, expiration status, date ranges, and more. Returns enhanced inventory items with human-readable names for all related entities."}, "response": []}, {"name": "Get Inventory Item", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/inventory/{{item_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "inventory", "{{item_id}}"]}, "description": "Retrieve detailed information about a specific inventory item with enhanced response including names."}, "response": []}, {"name": "Update Inventory Item", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"location_id\": \"updated-location-uuid\",\n    \"quantity\": 1.5,\n    \"expiration_date\": \"2024-03-15\",\n    \"notes\": \"Updated notes - moved to different location\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/inventory/{{item_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "inventory", "{{item_id}}"]}, "description": "Update an existing inventory item. Returns enhanced response with human-readable names."}, "response": []}, {"name": "Consume Inventory Item", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"quantity\": 0.5,\n    \"notes\": \"Used for dinner preparation\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/inventory/{{item_id}}/consume", "host": ["{{base_url}}"], "path": ["api", "v1", "inventory", "{{item_id}}", "consume"]}, "description": "Consume a specified quantity from an inventory item. Reduces the quantity and tracks usage."}, "response": []}]}, {"name": "Product Catalog - Categories", "description": "Product category management including hierarchical categories and subcategories.", "item": [{"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Dairy Products\",\n    \"description\": \"Milk, cheese, yogurt and other dairy items\",\n    \"parent_id\": null\n}"}, "url": {"raw": "{{base_url}}/api/v1/catalog/categories", "host": ["{{base_url}}"], "path": ["api", "v1", "catalog", "categories"]}, "description": "Create a new product category. Can be a root category or subcategory."}, "response": []}, {"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/catalog/categories", "host": ["{{base_url}}"], "path": ["api", "v1", "catalog", "categories"]}, "description": "Retrieve all product categories in a hierarchical structure."}, "response": []}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/catalog/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "catalog", "categories", "{{category_id}}"]}, "description": "Retrieve detailed information about a specific category including subcategories."}, "response": []}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Dairy Products\",\n    \"description\": \"Updated description for dairy products category\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/catalog/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "catalog", "categories", "{{category_id}}"]}, "description": "Update category information including name and description."}, "response": []}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/catalog/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "catalog", "categories", "{{category_id}}"]}, "description": "Delete a category. Cannot delete categories that have products or subcategories."}, "response": []}]}, {"name": "Product Catalog - Units", "description": "Unit of measure management including base units and derived units with conversion factors.", "item": [{"name": "Create Unit of Measure", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"kilogram\",\n    \"symbol\": \"kg\",\n    \"type\": \"weight\",\n    \"base_unit_id\": null,\n    \"conversion_factor\": null\n}"}, "url": {"raw": "{{base_url}}/api/v1/catalog/units", "host": ["{{base_url}}"], "path": ["api", "v1", "catalog", "units"]}, "description": "Create a new unit of measure. Can be a base unit or derived unit with conversion factor."}, "response": []}, {"name": "Get All Units", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/catalog/units", "host": ["{{base_url}}"], "path": ["api", "v1", "catalog", "units"]}, "description": "Retrieve all units of measure grouped by type (weight, volume, count, etc.)."}, "response": []}]}, {"name": "Recipe Management", "description": "Recipe creation, management, and ingredient checking against pantry inventory.", "item": [{"name": "Create Recipe", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Chocolate Chip Cookies\",\n    \"description\": \"Classic homemade chocolate chip cookies\",\n    \"prep_time_minutes\": 15,\n    \"cook_time_minutes\": 12,\n    \"servings\": 24,\n    \"difficulty\": \"easy\",\n    \"instructions\": [\n        {\n            \"step_number\": 1,\n            \"instruction\": \"Preheat oven to 375°F (190°C)\",\n            \"duration_minutes\": 5\n        },\n        {\n            \"step_number\": 2,\n            \"instruction\": \"Mix flour, baking soda, and salt in a bowl\",\n            \"duration_minutes\": 3\n        }\n    ],\n    \"ingredients\": [\n        {\n            \"product_variant_id\": \"flour-variant-uuid\",\n            \"quantity\": 2.25,\n            \"unit_id\": \"cup-unit-uuid\",\n            \"notes\": \"All-purpose flour\"\n        },\n        {\n            \"product_variant_id\": \"sugar-variant-uuid\",\n            \"quantity\": 0.75,\n            \"unit_id\": \"cup-unit-uuid\",\n            \"notes\": \"Granulated sugar\"\n        }\n    ],\n    \"tags\": [\"dessert\", \"cookies\", \"baking\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/recipes", "host": ["{{base_url}}"], "path": ["api", "v1", "recipes"]}, "description": "Create a new recipe with ingredients, instructions, and metadata."}, "response": []}, {"name": "Get All Recipes", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/recipes?page=1&limit=20&search=&tags=&difficulty=&max_prep_time=&max_cook_time=", "host": ["{{base_url}}"], "path": ["api", "v1", "recipes"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of recipes per page"}, {"key": "search", "value": "", "description": "Search term for recipe titles and descriptions"}, {"key": "tags", "value": "", "description": "Comma-separated list of tags to filter by"}, {"key": "difficulty", "value": "", "description": "Filter by difficulty level (easy, medium, hard)"}, {"key": "max_prep_time", "value": "", "description": "Maximum preparation time in minutes"}, {"key": "max_cook_time", "value": "", "description": "Maximum cooking time in minutes"}]}, "description": "Retrieve all recipes with filtering and pagination options."}, "response": []}, {"name": "Get Recipe by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/recipes/{{recipe_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "recipes", "{{recipe_id}}"]}, "description": "Retrieve detailed information about a specific recipe including ingredients and instructions."}, "response": []}, {"name": "Update Recipe", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Chocolate Chip Cookies\",\n    \"description\": \"Updated classic homemade chocolate chip cookies recipe\",\n    \"prep_time_minutes\": 20,\n    \"cook_time_minutes\": 15,\n    \"servings\": 30,\n    \"difficulty\": \"medium\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/recipes/{{recipe_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "recipes", "{{recipe_id}}"]}, "description": "Update recipe information. Only the recipe creator can update recipes."}, "response": []}, {"name": "Delete Recipe", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/recipes/{{recipe_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "recipes", "{{recipe_id}}"]}, "description": "Delete a recipe. Only the recipe creator can delete recipes."}, "response": []}, {"name": "Check Recipe Ingredients", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"pantry_id\": \"{{pantry_id}}\",\n    \"servings\": 12\n}"}, "url": {"raw": "{{base_url}}/api/v1/recipes/{{recipe_id}}/check-ingredients", "host": ["{{base_url}}"], "path": ["api", "v1", "recipes", "{{recipe_id}}", "check-ingredients"]}, "description": "Check if pantry has sufficient ingredients for a recipe. Returns availability status and missing items."}, "response": []}]}, {"name": "Pantry Locations", "description": "Manage storage locations within pantries (shelves, fridges, freezers, etc.).", "item": [{"name": "Create Pantry Location", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Top Shelf\",\n    \"description\": \"Upper shelf in main pantry\",\n    \"type\": \"shelf\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/locations", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "locations"]}, "description": "Create a new storage location within a pantry."}, "response": []}, {"name": "Get Pantry Locations", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/locations", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "locations"]}, "description": "Retrieve all storage locations in a pantry with item counts."}, "response": []}, {"name": "Update Pantry Location", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Top Shelf\",\n    \"description\": \"Updated description for top shelf\",\n    \"type\": \"shelf\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/locations/{{location_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "locations", "{{location_id}}"]}, "description": "Update a pantry location's information."}, "response": []}, {"name": "Delete Pantry Location", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/locations/{{location_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "locations", "{{location_id}}"]}, "description": "Delete a pantry location. Cannot delete locations that contain inventory items."}, "response": []}]}, {"name": "Shopping Lists", "description": "Shopping list management and automatic generation from recipe ingredients.", "item": [{"name": "Get Shopping List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/shopping-list", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "shopping-list"]}, "description": "Retrieve the current shopping list for a pantry with categorized items."}, "response": []}, {"name": "Add Shopping List Item", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"product_variant_id\": \"milk-variant-uuid\",\n    \"quantity\": 2,\n    \"unit_id\": \"liter-unit-uuid\",\n    \"notes\": \"Organic whole milk\",\n    \"priority\": \"medium\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/shopping-list/items", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "shopping-list", "items"]}, "description": "Add a new item to the shopping list."}, "response": []}, {"name": "Generate from Recipe", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"recipe_id\": \"{{recipe_id}}\",\n    \"servings\": 6,\n    \"check_inventory\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/shopping-list/generate-from-recipe", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "shopping-list", "generate-from-recipe"]}, "description": "Generate shopping list items from a recipe, checking against current inventory."}, "response": []}, {"name": "<PERSON> as Purchased", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"add_to_inventory\": true,\n    \"location_id\": \"{{location_id}}\",\n    \"expiration_date\": \"2024-02-15\",\n    \"cost\": 3.99\n}"}, "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/shopping-list/items/{{item_id}}/purchase", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "shopping-list", "items", "{{item_id}}", "purchase"]}, "description": "Mark a shopping list item as purchased and optionally add to inventory."}, "response": []}]}, {"name": "Expiration Tracking", "description": "Food expiration monitoring, alerts configuration, and expiring items tracking.", "item": [{"name": "Configure Global Alerts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"days_before_expiration\": 3,\n    \"notification_enabled\": true,\n    \"notification_channels\": [\"email\", \"push\"],\n    \"notification_time\": \"09:00\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/expiration/alerts/global", "host": ["{{base_url}}"], "path": ["api", "v1", "expiration", "alerts", "global"]}, "description": "Configure global expiration alert settings for the user."}, "response": []}, {"name": "Get Global Alert Configuration", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/expiration/alerts/global", "host": ["{{base_url}}"], "path": ["api", "v1", "expiration", "alerts", "global"]}, "description": "Retrieve current global expiration alert configuration."}, "response": []}, {"name": "Configure <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"pantry_id\": \"{{pantry_id}}\",\n    \"days_before_expiration\": 5,\n    \"notification_enabled\": true,\n    \"notification_channels\": [\"email\"],\n    \"notification_time\": \"08:00\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/expiration/alerts", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "expiration", "alerts"]}, "description": "Configure expiration alert settings for a specific pantry."}, "response": []}, {"name": "Get Expiring Items", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/pantries/{{pantry_id}}/expiration/items?days=7&include_expired=true", "host": ["{{base_url}}"], "path": ["api", "v1", "pantries", "{{pantry_id}}", "expiration", "items"], "query": [{"key": "days", "value": "7", "description": "Number of days to look ahead for expiring items"}, {"key": "include_expired", "value": "true", "description": "Include already expired items in the response"}]}, "description": "Get items that are expiring soon or have already expired in a pantry."}, "response": []}]}, {"name": "Health Check", "description": "API health and status endpoints.", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check API health status and database connectivity."}, "response": [{"name": "Healthy Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"healthy\",\n    \"timestamp\": \"2024-01-15T10:30:00Z\",\n    \"version\": \"2.0\",\n    \"database\": \"connected\",\n    \"uptime\": \"2h 15m 30s\"\n}"}]}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-refresh token if needed", "const accessToken = pm.environment.get('access_token');", "if (!accessToken && pm.request.url.path.join('/') !== 'api/v1/auth/login' && pm.request.url.path.join('/') !== 'api/v1/auth/register') {", "    console.log('No access token found. Please login first.');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test for all requests", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has proper JSON structure', function () {", "    if (pm.response.headers.get('Content-Type').includes('application/json')) {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('success');", "        pm.expect(jsonData).to.have.property('message');", "    }", "});"]}}], "variable": [{"key": "base_url", "value": "localhost:8080", "type": "string"}]}